package handler

import (
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"

	"gitee.com/masculine_girl/ginbase/api/core"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"

	"haha/store/model"
	"haha/store/vo"
	localTypes "haha/types"
	localUtils "haha/utils"
	"time"

	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/service"
	"gitee.com/masculine_girl/ginbase/api/service/nsq"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type OrderHandler struct {
	pkg.BaseHandler
	snowflake  *service.Snowflake
	db         *gorm.DB
	nsqService *nsq.NSQService
	// 商品库存锁，防止并发扣减库存时的竞态条件
	spuLocks sync.Map // map[uint]*sync.Mutex
}

func NewOrderHandler(app *core.AppServer, db *gorm.DB, snowflake *service.Snowflake, nsqService *nsq.NSQService) *OrderHandler {
	h := OrderHandler{
		db:         db,
		snowflake:  snowflake,
		nsqService: nsqService,
		spuLocks:   sync.Map{},
	}
	h.App = app
	return &h
}

// 获取商品锁
func (h *OrderHandler) getSpuLock(spuId uint) *sync.Mutex {
	lock, _ := h.spuLocks.LoadOrStore(spuId, &sync.Mutex{})
	return lock.(*sync.Mutex)
}

func (h *OrderHandler) CreateSpuOrder(c *gin.Context) {
	// 1. 验证和准备订单数据
	orderData, userID, err := h.validateAndPrepareOrderData(c)
	if err != nil {
		h.handleOrderErrorSimple(c, ErrInvalidParams, err.Error(), err)
		return
	}

	// 2. 获取商品锁
	locks := h.acquireProductLocks(orderData.ProductIds)
	defer h.releaseProductLocks(locks)

	// 3. 执行订单创建事务
	order, err := h.createOrderTransaction(orderData, userID)
	if err != nil {
		h.handleOrderErrorSimple(c, ErrDatabaseError, err.Error(), err)
		return
	}

	// 4. 异步处理后续操作
	h.handlePostOrderCreation(order, orderData, userID)

	// 5. 返回结果
	h.buildAndReturnOrderResponse(c, order)
}

// sendDelayedCancelMessage 发送延迟取消消息（独立方法，便于测试和维护）
func (h *OrderHandler) sendDelayedCancelMessage(orderInfo OrderInfo) {
	const maxRetries = 3
	const retryDelay = time.Second * 2

	for i := 0; i < maxRetries; i++ {
		// 使用正确的topic名称
		err := h.nsqService.Pub("haha_spu_order", orderInfo, 1800) // 30分钟延迟
		if err == nil {
			log.Printf("订单 %s 延迟取消消息发送成功", orderInfo.OrderNo)
			return
		}

		log.Printf("订单 %s 延迟取消消息发送失败，第%d次重试: %v", orderInfo.OrderNo, i+1, err)

		// 最后一次重试失败后记录错误并可能触发告警
		if i == maxRetries-1 {
			log.Printf("订单 %s 延迟取消消息发送最终失败，需要人工处理!", orderInfo.OrderNo)
			// 这里可以添加告警逻辑，如发送钉钉消息、邮件等
			h.handleCancelMessageFailure(orderInfo)
			return
		}

		// 等待后重试
		time.Sleep(retryDelay)
	}
}

// handleCancelMessageFailure 处理取消消息发送失败的情况
func (h *OrderHandler) handleCancelMessageFailure(orderInfo OrderInfo) {
	// 可以实现以下策略：
	// 1. 写入失败队列，后续补偿处理
	// 2. 发送告警通知
	// 3. 记录到专门的日志文件
	// 4. 触发人工介入流程

	log.Printf("CRITICAL: 订单 %s 无法发送自动取消消息，需要设置手动监控!", orderInfo.OrderNo)

	// 示例：写入失败记录表
	//failRecord := map[string]interface{}{
	//	"order_no":    orderInfo.OrderNo,
	//	"fail_reason": "NSQ延迟消息发送失败",
	//	"retry_count": 3,
	//	"created_at":  time.Now(),
	//}
	//
	//// 这里可以写入数据库失败记录表
	//if err := h.db.Table("tb_nsq_send_failures").Create(failRecord).Error; err != nil {
	//	log.Printf("记录NSQ发送失败也失败了: %v", err)
	//}
}

func (h *OrderHandler) AllList(c *gin.Context) {
	var data struct {
		Page     int `json:"page"`
		PageSize int `json:"page_size"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	session := h.db.Session(&gorm.Session{})
	var total int64
	session.Model(&model.Order{}).Count(&total)
	var items []model.Order
	var list = make([]vo.Order, 0)
	offset := (data.Page - 1) * data.PageSize
	res := session.Order("id DESC").Offset(offset).Limit(data.PageSize).Find(&items)
	if res.Error == nil {
		for _, item := range items {
			var order vo.Order
			err := utils.CopyObject(item, &order)
			if err == nil {
				order.Id = item.Id
				order.CreatedAt = item.CreatedAt.Unix()
				order.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, order)
			} else {
				fmt.Println(err)
			}
		}
	}
	resp.SUCCESS(c, baseVo.NewPage(total, data.Page, data.PageSize, list))
}

func (h *OrderHandler) List(c *gin.Context) {
	var data struct {
		Page     int `json:"page"`
		PageSize int `json:"page_size"`
		Status   int `json:"status"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint(userIDAny.(float64))
	session := h.db.Session(&gorm.Session{}).Where("user_id = ? ", userID)
	if data.Status > 0 {
		session.Where("status = ?", data.Status)
	} else {
		session.Where("status > ?", 0)
	}
	var total int64
	session.Model(&model.Order{}).Count(&total)
	var items []model.Order
	var list = make([]vo.Order, 0)
	offset := (data.Page - 1) * data.PageSize
	res := session.Order("id desc").Offset(offset).Limit(data.PageSize).Find(&items)
	if res.Error == nil {
		for _, item := range items {
			var order vo.Order
			err := utils.CopyObject(item, &order)
			if err == nil {
				order.Id = item.Id
				order.CreatedAt = item.CreatedAt.Unix()
				order.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, order)
			} else {
				fmt.Println(err)
			}
		}
	}
	resp.SUCCESS(c, baseVo.NewPage(total, data.Page, data.PageSize, list))
}

func (h *OrderHandler) Get(c *gin.Context) {
	key := h.GetInt(c, "id", 0)
	var order model.Order
	h.db.Where("id = ?", key).First(&order)
	var orderVo vo.Order
	err := utils.CopyObject(order, &orderVo)
	if err != nil {
		fmt.Println("对象拷贝失败：", err.Error())
		resp.ERROR(c, "获取服务失败")
		return
	}
	orderVo.Id = order.Id
	orderVo.CreatedAt = order.CreatedAt.Unix()
	resp.SUCCESS(c, orderVo)
}

func (h *OrderHandler) GetByOrderNo(c *gin.Context) {
	key := h.GetTrim(c, "order_no")
	fmt.Printf("查询订单详情，订单号: %s\n", key)

	var order model.Order
	err := h.db.Where("order_no = ?", key).First(&order).Error
	if err != nil {
		fmt.Printf("查询订单失败: %v\n", err)
		resp.ERROR(c, "订单不存在")
		return
	}

	fmt.Printf("订单状态: %d, 支付时间: %d, 支付方式: %s\n", order.Status, order.PayTime, order.PayWay)
	var orderVo vo.Order
	err = utils.CopyObject(order, &orderVo)
	if err != nil {
		fmt.Println("对象拷贝失败：", err.Error())
		resp.ERROR(c, "获取服务失败")
		return
	}
	orderVo.Id = order.Id
	orderVo.CreatedAt = order.CreatedAt.Unix()

	// 安全处理过期时间
	if !order.ExpireDate.IsZero() {
		orderVo.ExpireDate = order.ExpireDate.Unix()
	} else {
		orderVo.ExpireDate = 0
	}

	resp.SUCCESS(c, orderVo)
}

func (h *OrderHandler) GetOrderCount(c *gin.Context) {
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint(userIDAny.(float64))

	var orderCount vo.OrderCount
	waitPay := localTypes.OrderNotPaid
	waitReceive := localTypes.OrderPaidSuccess
	waitService := localTypes.OrderStatusDelivered
	afterSale := localTypes.OrderStatusReceived

	query := `
        SELECT 
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) AS waitPay,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) AS waitReceive,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) AS waitService,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) AS afterSale,
            COUNT(*) AS TotalCount
        FROM tb_orders
        WHERE user_id = ?;
    `
	// 使用gorm的Raw和Scan执行查询
	err := h.db.Raw(query, waitPay, waitReceive, waitService, afterSale, userID).Scan(&orderCount).Error

	if err != nil {
		resp.SUCCESS(c, orderCount)
		return
	}

	resp.SUCCESS(c, orderCount)
}

// 辅助方法：获取uint指针的值，如果为nil则返回0
func (h *OrderHandler) getUintValue(ptr *uint) uint {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// 辅助方法：获取套餐信息JSON
func (h *OrderHandler) getPackageInfo(spu model.Spu) (string, error) {
	if len(spu.PackageItems) == 0 {
		return "", nil
	}

	// 将套餐明细转换为JSON
	packageInfoBytes, err := json.Marshal(spu.PackageItems)
	if err != nil {
		log.Printf("套餐信息序列化失败: %v", err)
		return "", err
	}

	return string(packageInfoBytes), nil
}

// 辅助方法：确定单个商品的类型
func (h *OrderHandler) determineProductTypeFromSpu(spu model.Spu) string {
	// 如果商品有套餐明细，则为套餐类型
	if len(spu.PackageItems) > 0 {
		return "package"
	}
	return "single"
}

// validateAndPrepareOrderData 验证和准备订单数据
func (h *OrderHandler) validateAndPrepareOrderData(c *gin.Context) (*OrderData, uint, error) {
	var data localTypes.OrderRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		return nil, 0, fmt.Errorf("参数验证失败")
	}

	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		return nil, 0, fmt.Errorf("用户未登录")
	}
	userID := uint(userIDAny.(float64))

	// 预处理商品数据
	var productIds []uint
	goodsMap := make(map[uint]int)
	for _, p := range data.Goods {
		productIds = append(productIds, p.Id)
		goodsMap[p.Id] = p.Num
	}

	orderData := &OrderData{
		Request:    &data,
		ProductIds: productIds,
		GoodsMap:   goodsMap,
	}

	return orderData, userID, nil
}

// acquireProductLocks 获取商品锁
func (h *OrderHandler) acquireProductLocks(productIds []uint) []*sync.Mutex {
	// 按商品ID排序，防止死锁
	sortedIds := make([]uint, len(productIds))
	copy(sortedIds, productIds)
	sort.Slice(sortedIds, func(i, j int) bool {
		return sortedIds[i] < sortedIds[j]
	})

	// 按顺序获取锁，防止死锁
	locks := make([]*sync.Mutex, len(sortedIds))
	for i, id := range sortedIds {
		locks[i] = h.getSpuLock(id)
		locks[i].Lock()
	}
	return locks
}

// releaseProductLocks 释放商品锁
func (h *OrderHandler) releaseProductLocks(locks []*sync.Mutex) {
	// 按相反顺序释放锁
	for i := len(locks) - 1; i >= 0; i-- {
		locks[i].Unlock()
	}
}

// queryOrderData 查询订单相关数据（优化版本）
func (h *OrderHandler) queryOrderData(tx *gorm.DB, orderData *OrderData) error {
	// 1. 查询商品信息（使用悲观锁）+ 预加载套餐信息
	var products []model.Spu
	res := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("id in ?", orderData.ProductIds).
		Preload("PackageItems").
		Find(&products)
	if res.Error != nil {
		return fmt.Errorf("查询商品失败: %v", res.Error)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("没有找到商品")
	}
	orderData.Products = products

	// 2. 批量查询所有商品的分类信息
	var catIds []int
	catIdMap := make(map[int]bool)
	for _, product := range products {
		if !catIdMap[product.CatId] {
			catIds = append(catIds, product.CatId)
			catIdMap[product.CatId] = true
		}
	}

	if len(catIds) > 0 {
		var categories []model.Cat
		res = tx.Where("id in ?", catIds).Find(&categories)
		if res.Error != nil {
			return fmt.Errorf("查询商品分类失败: %v", res.Error)
		}

		// 创建分类映射
		catMap := make(map[int]model.Cat)
		for _, cat := range categories {
			catMap[int(cat.Id)] = cat
		}

		// 使用第一个商品的分类作为主要分类（保持原有逻辑）
		if len(products) > 0 {
			if cat, exists := catMap[products[0].CatId]; exists {
				orderData.ProductCat = cat
			}
		}
	}

	return nil
}

// loadTravelPassengers 查询并加载出行人信息
func (h *OrderHandler) loadTravelPassengers(orderVo *vo.Order, orderId uint) error {
	var passengers []model.TravelPassenger
	result := h.db.Where("order_id = ?", orderId).Find(&passengers)
	if result.Error != nil {
		return fmt.Errorf("查询出行人信息失败: %v", result.Error)
	}

	// 转换为VO对象
	if len(passengers) > 0 {
		var passengerVOs []vo.TravelPassengerVO
		for _, passenger := range passengers {
			passengerVO := vo.TravelPassengerVO{
				Id:            passenger.Id,
				OrderId:       passenger.OrderId,
				Name:          passenger.Name,
				IdCard:        passenger.IdCard,
				Phone:         passenger.Phone,
				PassengerType: passenger.PassengerType,
			}
			passengerVOs = append(passengerVOs, passengerVO)
		}
		orderVo.TravelPassengers = passengerVOs
	}

	return nil
}

// prepareOrderDetails 准备订单详情
func (h *OrderHandler) prepareOrderDetails(orderData *OrderData) error {
	var totalAmount float64
	var details []model.OrderDetail

	for _, p := range orderData.Products {
		requestNum := orderData.GoodsMap[p.Id]

		if p.StockNum < requestNum {
			return fmt.Errorf("%s库存不足，当前库存：%d，请求数量：%d", p.Name, p.StockNum, requestNum)
		}

		itemAmount := p.Price * float64(requestNum)
		totalAmount += itemAmount

		img, err := localUtils.GetFirstImageFromArray(p.Photo)
		if err != nil {
			img = ""
		}

		details = append(details, model.OrderDetail{
			SpuId:    p.Id,
			SpuName:  p.Name,
			SpuPhoto: img,
			SpuPrice: p.Price,
			SpuNum:   requestNum,
		})
	}

	orderData.TotalAmount = totalAmount
	orderData.OrderDetails = details
	return nil
}

// parseTimeString 解析时间字符串，支持多种格式
func (h *OrderHandler) parseTimeString(timeStr string) (time.Time, error) {
	// 支持的时间格式
	formats := []string{
		"2006-01-02T15:04:05Z07:00", // RFC3339
		"2006-01-02T15:04:05Z",      // RFC3339 UTC
		"2006-01-02T15:04:05",       // 无时区
		"2006-01-02 15:04:05",       // 常用格式
		"2006-01-02",                // 仅日期
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// generateOrderNo 生成订单号
func (h *OrderHandler) generateOrderNo() (string, error) {
	return h.snowflake.Next(true)
}

// createOrderRecord 创建订单记录
func (h *OrderHandler) createOrderRecord(tx *gorm.DB, orderData *OrderData, userID uint) (*model.Order, error) {
	orderNo, err := h.generateOrderNo()
	if err != nil {
		return nil, fmt.Errorf("生成订单号失败: %v", err)
	}
	orderData.OrderNo = orderNo

	// 解析酒店时间字段
	var hotelCheckinTime, hotelCheckoutTime *time.Time
	if orderData.Request.HotelCheckinTime != "" {
		if t, err := h.parseTimeString(orderData.Request.HotelCheckinTime); err == nil {
			hotelCheckinTime = &t
		}
	}
	if orderData.Request.HotelCheckoutTime != "" {
		if t, err := h.parseTimeString(orderData.Request.HotelCheckoutTime); err == nil {
			hotelCheckoutTime = &t
		}
	}

	// 创建订单
	order := model.Order{
		UserId:     userID,
		ProductId:  orderData.Request.ProductId,
		Subject:    orderData.Products[0].Name,
		Mobile:     orderData.Request.Mobile,
		OrderNo:    orderNo,
		Amount:     orderData.TotalAmount,
		Status:     1,
		OrderType:  orderData.Request.OrderType,
		ExpireDate: time.Now().Add(time.Second * time.Duration(1800)),
		UserName:   orderData.Request.UserName,
		UserPhone:  orderData.Request.UserPhone,
		Remark:     utils.JsonEncode(orderData.OrderDetails),

		// 酒店订单字段
		HotelCheckinTime:  hotelCheckinTime,
		HotelCheckoutTime: hotelCheckoutTime,
		HotelRoomType:     orderData.Request.HotelRoomType,
	}

	// 动态设置要忽略的字段
	omitFields := []string{"deleted_at"}
	omitFields = append(omitFields, "reservation_date")
	omitFields = append(omitFields, "reservation_status")

	res := tx.Omit(omitFields...).Create(&order)
	if res.Error != nil {
		return nil, fmt.Errorf("创建订单失败: %v", res.Error)
	}

	return &order, nil
}

// createOrderDetails 创建订单详情（优化版本）
func (h *OrderHandler) createOrderDetails(tx *gorm.DB, orderId uint, orderData *OrderData) error {
	// 批量设置订单ID
	for index := range orderData.OrderDetails {
		orderData.OrderDetails[index].OrderId = orderId
	}

	// 使用批量插入提升性能
	res := tx.CreateInBatches(&orderData.OrderDetails, 100) // 每批100条记录
	if res.Error != nil {
		return fmt.Errorf("创建订单详情失败: %v", res.Error)
	}

	return nil
}

// updateProductStock 更新商品库存（优化版本）
func (h *OrderHandler) updateProductStock(tx *gorm.DB, orderData *OrderData) error {

	// 方案1：逐个更新（简单可靠，适合小批量）
	if len(orderData.OrderDetails) <= 5 {
		return h.updateProductStockOneByOne(tx, orderData.OrderDetails)
	}

	// 方案2：批量更新（性能更好，适合大批量）
	return h.updateProductStockBatch(tx, orderData.OrderDetails)
}

// updateProductStockOneByOne 逐个更新商品库存（简单可靠）
func (h *OrderHandler) updateProductStockOneByOne(tx *gorm.DB, details []model.OrderDetail) error {
	for _, detail := range details {
		result := tx.Model(&model.Spu{}).
			Where("id = ? AND stock_num >= ?", detail.SpuId, detail.SpuNum).
			Update("stock_num", gorm.Expr("stock_num - ?", detail.SpuNum))

		if result.Error != nil {
			return fmt.Errorf("扣减商品 %d 库存失败: %v", detail.SpuId, result.Error)
		}

		if result.RowsAffected == 0 {
			return fmt.Errorf("商品 %d 库存不足，当前库存小于请求数量 %d", detail.SpuId, detail.SpuNum)
		}
	}
	return nil
}

// updateProductStockBatch 批量更新商品库存（性能更好）
func (h *OrderHandler) updateProductStockBatch(tx *gorm.DB, details []model.OrderDetail) error {
	// 使用strings.Builder优化字符串拼接
	var builder strings.Builder

	// 构建CASE语句
	builder.WriteString("UPDATE tb_spus SET stock_num = CASE id ")
	for _, detail := range details {
		builder.WriteString(fmt.Sprintf("WHEN %d THEN stock_num - %d ", detail.SpuId, detail.SpuNum))
	}
	builder.WriteString("END WHERE ")

	// 构建WHERE条件，确保库存充足
	whereConditions := make([]string, len(details))
	for i, detail := range details {
		whereConditions[i] = fmt.Sprintf("(id = %d AND stock_num >= %d)", detail.SpuId, detail.SpuNum)
	}
	builder.WriteString(strings.Join(whereConditions, " OR "))

	// 执行批量更新
	updateSQL := builder.String()
	result := tx.Exec(updateSQL)
	if result.Error != nil {
		return fmt.Errorf("批量扣减库存失败: %v", result.Error)
	}

	// 验证更新结果
	if int(result.RowsAffected) != len(details) {
		return fmt.Errorf("部分商品库存不足，成功更新 %d 个商品，请求更新 %d 个商品", result.RowsAffected, len(details))
	}

	return nil
}

// handlePostOrderCreation 处理订单创建后的操作（优化版本）
func (h *OrderHandler) handlePostOrderCreation(order *model.Order, orderData *OrderData, userID uint) {
	// 批量删除购物车中已下单的商品
	if len(orderData.Request.CartIds) > 0 {
		// 使用批量删除提升性能
		if err := h.db.Where("user_id = ? AND id IN ?", userID, orderData.Request.CartIds).Delete(&model.Cart{}).Error; err != nil {
			log.Printf("批量删除购物车商品失败: %v", err)
		} else {
			log.Printf("成功删除用户 %d 的 %d 个购物车商品", userID, len(orderData.Request.CartIds))
		}
	}

	// 发送延迟取消消息
	orderWait := OrderInfo{
		OrderNo:     orderData.OrderNo,
		Ids:         orderData.ProductIds,
		OrderDetail: orderData.OrderDetails,
	}
	go h.sendDelayedCancelMessage(orderWait)
}

// buildAndReturnOrderResponse 构建并返回订单响应
func (h *OrderHandler) buildAndReturnOrderResponse(c *gin.Context, order *model.Order) {
	var orderVo vo.Order
	err := utils.CopyObject(*order, &orderVo)
	if err != nil {
		log.Printf("复制订单对象失败: %v", err)
	}

	orderVo.Id = order.Id
	orderVo.CreatedAt = order.CreatedAt.Unix()
	orderVo.UpdatedAt = order.UpdatedAt.Unix()

	err = json.Unmarshal([]byte(order.Remark), &orderVo.Detail)
	if err != nil {
		log.Printf("解析订单详情失败: %v", err)
	}

	// 设置酒店订单时间戳
	if order.HotelCheckinTime != nil {
		checkinTimestamp := order.HotelCheckinTime.Unix()
		orderVo.HotelCheckinTime = &checkinTimestamp
	}
	if order.HotelCheckoutTime != nil {
		checkoutTimestamp := order.HotelCheckoutTime.Unix()
		orderVo.HotelCheckoutTime = &checkoutTimestamp
	}

	// 查询并设置出行人信息
	if err := h.loadTravelPassengers(&orderVo, order.Id); err != nil {
		log.Printf("查询出行人信息失败: %v", err)
	}

	resp.SUCCESS(c, orderVo)
}

// createOrderTransaction 执行订单创建事务
func (h *OrderHandler) createOrderTransaction(orderData *OrderData, userID uint) (*model.Order, error) {
	// 开始数据库事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			log.Printf("创建订单过程中发生panic: %v", r)
		}
	}()

	// 查询订单相关数据
	if err := h.queryOrderData(tx, orderData); err != nil {
		tx.Rollback()
		return nil, NewOrderError(ErrDatabaseError, "查询订单数据失败", err)
	}

	// 准备订单详情
	if err := h.prepareOrderDetails(orderData); err != nil {
		tx.Rollback()
		return nil, NewOrderError(ErrStockInsufficient, "准备订单详情失败", err)
	}

	// 创建订单记录
	order, err := h.createOrderRecord(tx, orderData, userID)
	if err != nil {
		tx.Rollback()
		return nil, NewOrderError(ErrDatabaseError, "创建订单记录失败", err)
	}

	// 创建订单详情
	if err := h.createOrderDetails(tx, order.Id, orderData); err != nil {
		tx.Rollback()
		return nil, NewOrderError(ErrDatabaseError, "创建订单详情失败", err)
	}

	// 更新商品库存
	if err := h.updateProductStock(tx, orderData); err != nil {
		tx.Rollback()
		return nil, NewOrderError(ErrStockInsufficient, "更新商品库存失败", err)
	}

	// 保存出行人信息（仅线路订单）
	if err := h.saveTravelPassengers(tx, order.Id, orderData); err != nil {
		tx.Rollback()
		return nil, NewOrderError(ErrDatabaseError, "保存出行人信息失败", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, NewOrderError(ErrDatabaseError, "提交订单失败", err)
	}

	return order, nil
}

// 错误码定义
const (
	ErrInvalidParams     = 1001
	ErrStockInsufficient = 1002
	ErrDatabaseError     = 1003
	ErrReservationFailed = 1004
	ErrTimeSlotInvalid   = 1005
	ErrUserNotLoggedIn   = 1006
	ErrProductNotFound   = 1007
)

// Error 实现error接口
func (e *OrderError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("[%d] %s: %v", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// NewOrderError 创建订单错误
func NewOrderError(code int, message string, err error) *OrderError {
	return &OrderError{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// handleOrderError 统一处理订单错误
func (h *OrderHandler) handleOrderError(tx *gorm.DB, c *gin.Context, err *OrderError) {
	if tx != nil {
		tx.Rollback()
	}
	log.Printf("订单创建失败 [%d]: %s, 错误: %v", err.Code, err.Message, err.Err)
	resp.ERROR(c, err.Message)
}

// handleOrderErrorSimple 简化版错误处理（无事务）
func (h *OrderHandler) handleOrderErrorSimple(c *gin.Context, code int, message string, err error) {
	log.Printf("订单操作失败 [%d]: %s, 错误: %v", code, message, err)
	resp.ERROR(c, message)
}

// saveTravelPassengers 保存出行人信息（仅线路订单）
func (h *OrderHandler) saveTravelPassengers(tx *gorm.DB, orderId uint, orderData *OrderData) error {
	// 只有线路订单才需要保存出行人信息
	if len(orderData.Request.TravelPassengers) == 0 {
		return nil
	}

	// 准备出行人数据
	var passengers []model.TravelPassenger
	for _, passengerReq := range orderData.Request.TravelPassengers {
		passenger := model.TravelPassenger{
			OrderId:       orderId,
			Name:          passengerReq.Name,
			IdCard:        passengerReq.IdCard,
			Phone:         passengerReq.Phone,
			PassengerType: passengerReq.PassengerType,
		}
		passengers = append(passengers, passenger)
	}

	// 批量插入出行人信息
	if len(passengers) > 0 {
		result := tx.CreateInBatches(&passengers, 100)
		if result.Error != nil {
			return fmt.Errorf("批量保存出行人信息失败: %v", result.Error)
		}
		log.Printf("成功保存 %d 个出行人信息", len(passengers))
	}

	return nil
}
