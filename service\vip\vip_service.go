package vip

import (
	"fmt"
	"haha/store/model"
	"haha/store/vo"
	"haha/types"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type VipService struct {
	db    *gorm.DB
	redis *redis.Client
}

func NewVipService(db *gorm.DB, redis *redis.Client) *VipService {
	return &VipService{
		db:    db,
		redis: redis,
	}
}

// PurchaseVip VIP购买
func (s *VipService) PurchaseVip(c *gin.Context, userID uint64, req vo.VipPurchaseRequest) (*vo.VipPurchaseResponse, error) {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 验证VIP等级是否存在且有效
	var vipLevel model.VipLevel
	if err := tx.Where("id = ? AND is_active = ?", req.VipLevelId, true).First(&vipLevel).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("VIP等级不存在或已禁用")
	}

	// 2. 生成订单号
	orderNo := s.generateOrderNo()

	// 3. 创建订单记录
	order := model.Order{
		UserId:     uint(userID),
		ProductId:  uint(req.VipLevelId),
		OrderNo:    orderNo,
		Subject:    fmt.Sprintf("VIP会员-%s", vipLevel.LevelName),
		Amount:     vipLevel.Price,
		Status:     types.OrderNotPaid,               // 待支付状态
		ExpireDate: time.Now().Add(30 * time.Minute), // 30分钟过期
	}

	if err := tx.Create(&order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建订单失败")
	}

	// 4. 创建订单详情记录
	orderDetail := model.OrderDetail{
		OrderId:  order.Id,
		SpuId:    uint(req.VipLevelId),
		SpuName:  vipLevel.LevelName,
		SpuNum:   1,
		SpuPrice: vipLevel.Price,
	}

	if err := tx.Create(&orderDetail).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建订单详情失败")
	}

	// 5. 创建用户VIP记录（与订单一对一关联）
	userVip := model.UserVip{
		UserId:        userID,
		VipLevelId:    req.VipLevelId,
		OrderNo:       &orderNo,   // 关联订单号
		StartTime:     time.Now(), // 支付成功后设置
		EndTime:       nil,        // 支付成功后设置
		PurchasePrice: vipLevel.Price,
		PaymentMethod: req.PaymentMethod,
		Status:        "PENDING", // 待支付状态
		AutoRenew:     req.AutoRenew,
		PurchaseTime:  time.Now(),
	}

	if err := tx.Create(&userVip).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建VIP记录失败")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败")
	}

	// 6. 返回购买结果
	result := &vo.VipPurchaseResponse{
		OrderNo:        orderNo,
		VipLevelId:     req.VipLevelId,
		VipLevelName:   vipLevel.LevelName,
		Price:          vipLevel.Price,
		DurationMonths: vipLevel.DurationMonths,
		PaymentUrl:     nil, // 前端直接调用支付接口
	}

	return result, nil
}

// ActivateVip 激活VIP（支付成功后调用）
func (s *VipService) ActivateVip(orderNo string) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 查询订单信息
	var order model.Order
	if err := tx.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("查询订单失败")
	}

	// 2. 获取VIP等级信息
	var vipLevel model.VipLevel
	if err := tx.Where("id = ?", order.ProductId).First(&vipLevel).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("获取VIP等级信息失败")
	}

	// 3. 计算VIP开始和结束时间
	now := time.Now()
	endTime := now.AddDate(0, vipLevel.DurationMonths, 0)

	// 4. 更新用户VIP记录
	if err := tx.Model(&model.UserVip{}).
		Where("order_no = ? AND status = ?", orderNo, "PENDING").
		Updates(map[string]interface{}{
			"status":     "ACTIVE",
			"start_time": now,
			"end_time":   endTime,
		}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新VIP状态失败")
	}

	// 5. 获取该VIP等级的所有权益
	var levelBenefits []model.VipLevelBenefit
	if err := tx.Where("vip_level_id = ? AND is_active = ?", order.ProductId, true).Find(&levelBenefits).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("获取VIP权益失败")
	}

	// 6. 为用户创建权益记录
	for _, levelBenefit := range levelBenefits {
		remainingCount := levelBenefit.BenefitLimit
		userVipBenefit := model.UserVipBenefit{
			UserId:         uint64(order.UserId),
			VipLevelId:     uint64(order.ProductId),
			BenefitId:      levelBenefit.BenefitId,
			BenefitValue:   levelBenefit.BenefitValue,
			BenefitLimit:   levelBenefit.BenefitLimit,
			UsedCount:      0,
			RemainingCount: remainingCount,
			StartTime:      now,
			EndTime:        &endTime,
			Status:         "ACTIVE",
			IsExpired:      false,
		}

		if err := tx.Create(&userVipBenefit).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建用户权益记录失败")
		}
	}

	// 7. 更新或创建用户当前VIP状态
	daysRemaining := s.calculateRemainingDays(&endTime)
	userCurrentVip := model.UserCurrentVip{
		UserId:        uint64(order.UserId),
		VipLevelId:    &vipLevel.Id,
		VipLevelName:  &vipLevel.LevelName,
		VipLevelCode:  &vipLevel.LevelCode,
		VipLevelRank:  &vipLevel.LevelRank,
		StartTime:     &now,
		EndTime:       &endTime,
		DaysRemaining: daysRemaining,
		IsExpired:     false,
		UpdatedAt:     now,
	}

	// 使用Save方法，如果记录存在则更新，不存在则创建
	if err := tx.Save(&userCurrentVip).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新用户当前VIP状态失败")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败")
	}

	return nil
}

// SetAutoRenew 设置自动续费状态
func (s *VipService) SetAutoRenew(userID uint64, autoRenew bool) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新用户当前VIP状态的自动续费标识
	if err := tx.Model(&model.UserCurrentVip{}).
		Where("user_id = ?", userID).
		Update("auto_renew", autoRenew).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新自动续费状态失败")
	}

	// 2. 更新最新的VIP记录的自动续费标识
	if err := tx.Model(&model.UserVip{}).
		Where("user_id = ? AND status = ?", userID, "ACTIVE").
		Order("created_at DESC").
		Limit(1).
		Update("auto_renew", autoRenew).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新VIP记录自动续费状态失败")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败")
	}

	return nil
}

// ProcessAutoRenewal 处理自动续费（定时任务调用）
func (s *VipService) ProcessAutoRenewal() error {
	// 查找需要自动续费的用户（VIP即将到期且开启了自动续费）
	now := time.Now()
	renewalThreshold := now.AddDate(0, 0, 7) // 提前7天开始处理自动续费

	var usersToRenew []model.UserCurrentVip
	if err := s.db.Where("auto_renew = ? AND end_time <= ? AND is_expired = ?",
		true, renewalThreshold, false).Find(&usersToRenew).Error; err != nil {
		return fmt.Errorf("查询需要自动续费的用户失败: %v", err)
	}

	for _, userVip := range usersToRenew {
		if err := s.processUserAutoRenewal(userVip); err != nil {
			fmt.Printf("处理用户 %d 自动续费失败: %v\n", userVip.UserId, err)
			continue
		}
	}

	return nil
}

// processUserAutoRenewal 处理单个用户的自动续费
func (s *VipService) processUserAutoRenewal(userVip model.UserCurrentVip) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 获取VIP等级信息
	var vipLevel model.VipLevel
	if err := tx.Where("id = ?", userVip.VipLevelId).First(&vipLevel).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("获取VIP等级信息失败")
	}

	// 2. 生成新的订单号
	orderNo := s.generateOrderNo()

	// 3. 创建新的订单记录
	order := model.Order{
		UserId:    uint(userVip.UserId),
		ProductId: uint(*userVip.VipLevelId),
		OrderNo:   orderNo,
		Subject:   fmt.Sprintf("VIP自动续费-%s", *userVip.VipLevelName),
		Amount:    vipLevel.Price,
		Status:    types.OrderPaidSuccess, // 自动续费直接设为支付成功
		PayTime:   time.Now().Unix(),
		PayWay:    "自动续费",
	}

	if err := tx.Create(&order).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("创建自动续费订单失败")
	}

	// 4. 创建新的VIP记录
	now := time.Now()
	endTime := now.AddDate(0, vipLevel.DurationMonths, 0)

	newUserVip := model.UserVip{
		UserId:        userVip.UserId,
		VipLevelId:    *userVip.VipLevelId,
		OrderNo:       &orderNo,
		StartTime:     now,
		EndTime:       &endTime,
		PurchasePrice: vipLevel.Price,
		PaymentMethod: &order.PayWay,
		Status:        "ACTIVE",
		AutoRenew:     true, // 保持自动续费状态
		PurchaseTime:  now,
	}

	if err := tx.Create(&newUserVip).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("创建自动续费VIP记录失败")
	}

	// 5. 更新用户当前VIP状态
	daysRemaining := s.calculateRemainingDays(&endTime)
	userCurrentVip := model.UserCurrentVip{
		UserId:        userVip.UserId,
		VipLevelId:    userVip.VipLevelId,
		VipLevelName:  userVip.VipLevelName,
		VipLevelCode:  userVip.VipLevelCode,
		VipLevelRank:  userVip.VipLevelRank,
		StartTime:     &now,
		EndTime:       &endTime,
		DaysRemaining: daysRemaining,
		IsExpired:     false,
		UpdatedAt:     now,
	}

	if err := tx.Save(&userCurrentVip).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新用户当前VIP状态失败")
	}

	// 6. 创建新的权益记录
	var levelBenefits []model.VipLevelBenefit
	if err := tx.Where("vip_level_id = ? AND is_active = ?", *userVip.VipLevelId, true).Find(&levelBenefits).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("获取VIP权益失败")
	}

	for _, levelBenefit := range levelBenefits {
		remainingCount := levelBenefit.BenefitLimit
		userVipBenefit := model.UserVipBenefit{
			UserId:         userVip.UserId,
			VipLevelId:     *userVip.VipLevelId,
			BenefitId:      levelBenefit.BenefitId,
			BenefitValue:   levelBenefit.BenefitValue,
			BenefitLimit:   levelBenefit.BenefitLimit,
			UsedCount:      0,
			RemainingCount: remainingCount,
			StartTime:      now,
			EndTime:        &endTime,
			Status:         "ACTIVE",
			IsExpired:      false,
		}

		if err := tx.Create(&userVipBenefit).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建用户权益记录失败")
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败")
	}

	fmt.Printf("用户 %d VIP自动续费成功，新订单号: %s\n", userVip.UserId, orderNo)
	return nil
}

// CancelVip 取消VIP（支付失败或用户取消）
func (s *VipService) CancelVip(orderNo string) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新订单状态为已取消
	if err := tx.Model(&model.Order{}).
		Where("order_no = ?", orderNo).
		Update("status", types.OrderStatusCancel).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新订单状态失败")
	}

	// 2. 更新VIP记录状态为已取消
	if err := tx.Model(&model.UserVip{}).
		Where("order_no = ?", orderNo).
		Update("status", "CANCELLED").Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新VIP状态失败")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败")
	}

	return nil
}

// generateOrderNo 生成订单号
func (s *VipService) generateOrderNo() string {
	return fmt.Sprintf("VIP%d", time.Now().UnixNano())
}

// calculateRemainingDays 计算剩余天数
func (s *VipService) calculateRemainingDays(endTime *time.Time) *int {
	if endTime == nil {
		return nil
	}

	now := time.Now()
	remainingDays := int(endTime.Sub(now).Hours() / 24)
	if remainingDays < 0 {
		remainingDays = 0
	}
	return &remainingDays
}
