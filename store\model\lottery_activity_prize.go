package model

import (
	"gitee.com/masculine_girl/ginbase/api/store/model"
	"github.com/shopspring/decimal"
)

// LotteryActivityPrize 活动与奖品关联表
type LotteryActivityPrize struct {
	model.GinBaseModel
	ActivityId        uint            `gorm:"not null;index;comment:抽奖活动ID" json:"activityId"`
	PrizeId           uint            `gorm:"not null;index;comment:奖品ID" json:"prizeId"`
	TotalQuantity     uint            `gorm:"default:0;comment:奖品总数量" json:"totalQuantity"`
	RemainingQuantity uint            `gorm:"default:0;comment:奖品剩余数量" json:"remainingQuantity"`
	Probability       decimal.Decimal `gorm:"type:decimal(5,4);default:0.0000;comment:中奖概率 (0到1之间)" json:"probability"`

	// 关联字段
	Activity *LotteryActivity `gorm:"foreignKey:ActivityId" json:"activity,omitempty"`
	Prize    *LotteryPrize    `gorm:"foreignKey:PrizeId" json:"prize,omitempty"`
}

func (LotteryActivityPrize) TableName() string {
	return "tb_lottery_activity_prizes"
}
