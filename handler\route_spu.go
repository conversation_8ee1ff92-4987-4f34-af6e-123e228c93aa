package handler

import (
	"context"
	"fmt"
	"haha/store/model"
	"haha/store/vo"
	"math"
	"time"

	"encoding/json"
	"log"
	"strconv"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseModel "gitee.com/masculine_girl/ginbase/api/store/model"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type RouteSpuHandler struct {
	pkg.BaseHandler
	db    *gorm.DB
	redis *redis.Client
}

func NewRouteSpuHandler(app *core.AppServer, db *gorm.DB, redis *redis.Client) *RouteSpuHandler {
	h := RouteSpuHandler{
		db:    db,
		redis: redis,
	}
	h.App = app
	return &h
}

func (h *RouteSpuHandler) List(c *gin.Context) {
	page := h.GetInt(c, "page", 1)
	pageSize := h.GetInt(c, "page_size", 20)
	offset := (page - 1) * pageSize

	cid := h.GetInt(c, "cid", 0)

	queryParam := h.GetTrim(c, "query")
	sort := h.GetTrim(c, "sort")
	var total int64
	var items []model.Spu
	var list = make([]vo.Spu, 0)
	session := h.db.Session(&gorm.Session{})
	// 构建查询条件
	session = session.Model(&model.Spu{})

	// 添加线路商品过滤条件
	session = session.Where("product_type = ?", "route")

	if cid > 0 {
		session = session.Where("cat_id = ?", cid)
	}

	if queryParam != "" {
		session, _ = baseModel.BuildQuery(session, queryParam)
	}
	session.Count(&total)
	if sort != "" {
		session = session.Order(fmt.Sprintf("%s desc", sort))
	} else {
		session = session.Order("id desc")
	}
	res := session.Offset(offset).Limit(pageSize).Find(&items)
	if res.Error == nil {

		for _, item := range items {
			var spuVo vo.Spu
			err := utils.CopyObject(item, &spuVo)
			if err == nil {
				spuVo.Id = item.Id
				spuVo.CreatedAt = item.CreatedAt.Unix()
				spuVo.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, spuVo)
			} else {
				fmt.Println(err)
			}
		}

		// 计算会员价格和节省金额
		list = h.calculateVipPricing(list)
	}
	resp.SUCCESS(c, baseVo.NewPage(total, page, pageSize, list))
}

func (h *RouteSpuHandler) RecommendsList(c *gin.Context) {
	page := h.GetInt(c, "page", 1)
	pageSize := h.GetInt(c, "page_size", 20)
	offset := (page - 1) * pageSize

	var total int64
	var items []model.Spu
	var list = make([]vo.Spu, 0)
	session := h.db.Session(&gorm.Session{})
	// 构建查询条件
	session = session.Model(&model.Spu{})
	// 添加线路商品过滤条件
	session = session.Where("product_type = ?", "route")
	session = session.Where("recommends > ?", 0)
	session.Count(&total)
	res := session.Order("recommends desc").Offset(offset).Limit(pageSize).Find(&items)
	if res.Error == nil {

		for _, item := range items {
			var spuVo vo.Spu
			err := utils.CopyObject(item, &spuVo)
			if err == nil {
				spuVo.Id = item.Id
				spuVo.CreatedAt = item.CreatedAt.Unix()
				spuVo.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, spuVo)
			} else {
				fmt.Println(err)
			}
		}

		// 计算会员价格和节省金额
		list = h.calculateVipPricing(list)
	}
	resp.SUCCESS(c, baseVo.NewPage(total, page, pageSize, list))
}

func (h *RouteSpuHandler) Get(c *gin.Context) {
	key := h.GetInt(c, "id", 0)
	if key == 0 {
		resp.ERROR(c, "商品ID不能为空")
		return
	}

	ctx := context.Background()
	cacheKey := fmt.Sprintf("route-spu:detail:%d", key)

	// 先尝试从缓存获取
	if h.redis != nil {
		cached, err := h.redis.Get(ctx, cacheKey).Result()
		if err == nil && cached != "" {
			var spuVo vo.Spu
			if json.Unmarshal([]byte(cached), &spuVo) == nil {
				resp.SUCCESS(c, spuVo)
				return
			}
		}
	}

	// 优化查询：一次性预加载所有相关数据
	var spu model.Spu
	if err := h.db.Preload("PackageItems.Spu").Where("id = ? AND product_type = ?", key, "route").First(&spu).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			resp.ERROR(c, "商品不存在")
		} else {
			resp.ERROR(c, "获取商品信息失败")
		}
		return
	}

	// 构建返回数据
	var spuVo vo.Spu
	err := utils.CopyObject(spu, &spuVo)
	if err != nil {
		resp.ERROR(c, "获取服务失败")
		return
	}
	spuVo.Id = spu.Id
	spuVo.CreatedAt = spu.CreatedAt.Unix()
	spuVo.UpdatedAt = spu.UpdatedAt.Unix()
	spuVo.SupportReservation = true

	// 转换套餐明细为VO（数据已预加载）
	spuVo.PackageItems = make([]vo.PackageItem, 0, len(spu.PackageItems))
	for _, item := range spu.PackageItems {
		var packageItemVo vo.PackageItem
		if err := utils.CopyObject(item, &packageItemVo); err == nil {
			packageItemVo.Id = item.Id
			packageItemVo.CreatedAt = item.CreatedAt.Unix()
			packageItemVo.UpdatedAt = item.UpdatedAt.Unix()
			packageItemVo.SpuName = item.Spu.Name
			packageItemVo.SpuPhoto = item.Spu.Photo
			packageItemVo.SpuPrice = item.Spu.Price
			spuVo.PackageItems = append(spuVo.PackageItems, packageItemVo)
		}
	}

	// 计算会员价格和节省金额
	spuList := []vo.Spu{spuVo}
	spuList = h.calculateVipPricing(spuList)
	spuVo = spuList[0]

	// 缓存结果
	if h.redis != nil {
		jsonData, _ := json.Marshal(spuVo)
		h.redis.Set(ctx, cacheKey, jsonData, 10*time.Minute) // 缓存10分钟
	}

	resp.SUCCESS(c, spuVo)
}

// InvalidateRouteSpuCache 清除线路商品相关缓存（当商品信息更新时调用）
func (h *RouteSpuHandler) InvalidateRouteSpuCache(spuId uint) {
	if h.redis == nil {
		return
	}

	ctx := context.Background()

	// 清除商品详情缓存
	detailKey := fmt.Sprintf("route-spu:detail:%d", spuId)
	h.redis.Del(ctx, detailKey)

	log.Printf("清除线路商品 %d 相关缓存", spuId)
}

// getVipDiscount 获取会员折扣配置
func (h *RouteSpuHandler) getVipDiscount() (*model.VipLevelBenefit, error) {
	ctx := context.Background()
	cacheKey := "vip:discount:level1:benefit2"

	// 先从缓存查询
	if h.redis != nil {
		cached, err := h.redis.Get(ctx, cacheKey).Result()
		if err == nil && cached != "" {
			var vipBenefit model.VipLevelBenefit
			if json.Unmarshal([]byte(cached), &vipBenefit) == nil {
				return &vipBenefit, nil
			}
		}
	}

	// 从数据库查询
	var vipBenefit model.VipLevelBenefit
	err := h.db.Where("vip_level_id = ? AND benefit_id = ? AND is_active = ?", 1, 2, true).First(&vipBenefit).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 缓存空结果
			if h.redis != nil {
				h.redis.Set(ctx, cacheKey, "", 5*time.Minute)
			}
			return nil, nil
		}
		return nil, err
	}

	// 缓存结果
	if h.redis != nil {
		jsonData, _ := json.Marshal(vipBenefit)
		h.redis.Set(ctx, cacheKey, jsonData, 10*time.Minute)
	}

	return &vipBenefit, nil
}

// calculateVipPricing 批量计算会员价格和节省金额
func (h *RouteSpuHandler) calculateVipPricing(spuList []vo.Spu) []vo.Spu {
	// 获取会员折扣配置
	vipBenefit, err := h.getVipDiscount()
	if err != nil || vipBenefit == nil || vipBenefit.BenefitValue == nil {
		// 没有会员折扣配置，直接返回原列表
		return spuList
	}

	// 解析折扣率
	discountRate, err := strconv.ParseFloat(*vipBenefit.BenefitValue, 64)
	if err != nil || discountRate <= 0 || discountRate >= 1 {
		// 折扣率无效，直接返回原列表
		return spuList
	}

	// 计算每个商品的会员价格和节省金额
	for i := range spuList {
		vipPrice := math.Round(spuList[i].Price*discountRate*100) / 100
		savings := math.Round((spuList[i].Price-vipPrice)*100) / 100

		spuList[i].VipPrice = &vipPrice
		spuList[i].VipSavings = &savings
	}

	return spuList
}
