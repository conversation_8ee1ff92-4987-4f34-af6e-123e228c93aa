package handler

import (
	"strconv"

	"haha/store/model"
	"haha/store/vo"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UserPassengerHandler 用户出行人处理器
type UserPassengerHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

// NewUserPassengerHandler 创建用户出行人处理器
func NewUserPassengerHandler(app *core.AppServer, db *gorm.DB) *UserPassengerHandler {
	h := &UserPassengerHandler{db: db}
	h.App = app
	return h
}

// Create 创建用户出行人
func (h *UserPassengerHandler) Create(c *gin.Context) {
	var req vo.UserPassengerRequest
	if err := c.Should<PERSON>(&req); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	// 获取用户ID
	userIdInterface, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userId := uint(userIdInterface.(float64))

	// 如果设置为默认出行人，先取消其他默认出行人
	if req.IsDefault {
		h.db.Model(&model.UserPassenger{}).Where("user_id = ?", userId).Update("is_default", false)
	}

	// 创建出行人
	userPassenger := model.UserPassenger{
		UserId:        userId,
		Name:          req.Name,
		IdCard:        req.IdCard,
		Phone:         req.Phone,
		PassengerType: req.PassengerType,
		IsDefault:     req.IsDefault,
	}

	if err := h.db.Create(&userPassenger).Error; err != nil {
		resp.ERROR(c, "创建出行人失败")
		return
	}

	resp.SUCCESS(c, h.convertToVO(&userPassenger))
}

// List 获取用户出行人列表
func (h *UserPassengerHandler) List(c *gin.Context) {
	// 获取用户ID
	userIdInterface, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userId := uint(userIdInterface.(float64))

	var passengers []model.UserPassenger
	if err := h.db.Where("user_id = ?", userId).Order("is_default DESC, created_at DESC").Find(&passengers).Error; err != nil {
		resp.ERROR(c, "查询出行人失败")
		return
	}

	// 转换为VO
	var passengerVOs []vo.UserPassengerVO
	for _, passenger := range passengers {
		passengerVOs = append(passengerVOs, *h.convertToVO(&passenger))
	}

	resp.SUCCESS(c, passengerVOs)
}

// Update 更新用户出行人
func (h *UserPassengerHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		resp.ERROR(c, "无效的ID")
		return
	}

	var req vo.UserPassengerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	// 获取用户ID
	userIdInterface, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userId := uint(userIdInterface.(float64))

	// 查找出行人
	var passenger model.UserPassenger
	if err := h.db.Where("id = ? AND user_id = ?", id, userId).First(&passenger).Error; err != nil {
		resp.ERROR(c, "出行人不存在")
		return
	}

	// 如果设置为默认出行人，先取消其他默认出行人
	if req.IsDefault && !passenger.IsDefault {
		h.db.Model(&model.UserPassenger{}).Where("user_id = ? AND id != ?", userId, id).Update("is_default", false)
	}

	// 更新出行人信息
	passenger.Name = req.Name
	passenger.IdCard = req.IdCard
	passenger.Phone = req.Phone
	passenger.PassengerType = req.PassengerType
	passenger.IsDefault = req.IsDefault

	if err := h.db.Save(&passenger).Error; err != nil {
		resp.ERROR(c, "更新出行人失败")
		return
	}

	resp.SUCCESS(c, h.convertToVO(&passenger))
}

// Delete 删除用户出行人
func (h *UserPassengerHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		resp.ERROR(c, "无效的ID")
		return
	}

	// 获取用户ID
	userIdInterface, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userId := uint(userIdInterface.(float64))

	// 删除出行人
	result := h.db.Where("id = ? AND user_id = ?", id, userId).Delete(&model.UserPassenger{})
	if result.Error != nil {
		resp.ERROR(c, "删除出行人失败")
		return
	}

	if result.RowsAffected == 0 {
		resp.ERROR(c, "出行人不存在")
		return
	}

	resp.SUCCESS(c, nil)
}

// convertToVO 转换为VO对象
func (h *UserPassengerHandler) convertToVO(passenger *model.UserPassenger) *vo.UserPassengerVO {
	return &vo.UserPassengerVO{
		Id:            passenger.Id,
		UserId:        passenger.UserId,
		Name:          passenger.Name,
		IdCard:        passenger.IdCard,
		Phone:         passenger.Phone,
		PassengerType: passenger.PassengerType,
		IsDefault:     passenger.IsDefault,
		CreatedAt:     passenger.CreatedAt,
		UpdatedAt:     passenger.UpdatedAt,
	}
}
