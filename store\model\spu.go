package model

import "gitee.com/masculine_girl/ginbase/api/store/model"

type Spu struct {
	model.GinBaseModel
	Name        string
	Photo       string
	Info        string
	Price       float64
	VipPrice    float64
	Description string
	StockNum    int
	SalesNum    int
	CatId       int
	CatPath     string
	Likes       int
	Recommends  *int

	// 商品类型相关字段
	ProductType string `gorm:"default:'route'"` // 商品类型:common=通用 route=线路 hotel=酒店

	// 地理位置相关字段
	Province  string   `gorm:"size:20"`            // 省份
	City      string   `gorm:"size:30"`            // 城市
	District  string   `gorm:"size:30"`            // 区/县
	Address   string   `gorm:"size:200"`           // 详细地址
	Longitude *float64 `gorm:"type:decimal(10,6)"` // 经度
	Latitude  *float64 `gorm:"type:decimal(10,6)"` // 纬度

	// 酒店相关字段
	HotelBrand   string `gorm:"size:50"` // 酒店品牌
	StarLevel    *int   // 星级 1-5星
	HotelType    string `gorm:"size:20"`   // 酒店类型 luxury=豪华 business=商务 resort=度假 budget=经济
	Facilities   string `gorm:"type:text"` // 酒店设施列表
	Services     string `gorm:"type:text"` // 酒店服务列表
	CheckinTime  string `gorm:"size:10"`   // 入住时间 如14:00
	CheckoutTime string `gorm:"size:10"`   // 退房时间 如12:00
	ContactPhone string `gorm:"size:20"`   // 酒店联系电话
	RoomType     string `gorm:"size:50"`   // 主要房型
	RoomArea     *int   // 房间面积平米

	// 关联的套餐明细（当前商品作为套餐时）
	PackageItems []PackageItem `gorm:"foreignKey:PackageId"`
}
