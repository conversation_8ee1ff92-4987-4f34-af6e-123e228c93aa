package vo

import (
	"time"
)

// VipBenefitVO VIP权益VO
type VipBenefitVO struct {
	Id          uint64    `json:"id"`
	BenefitName string    `json:"benefitName"`
	BenefitCode *string   `json:"benefitCode"`
	BenefitType string    `json:"benefitType"`
	Description *string   `json:"description"`
	IconUrl     *string   `json:"iconUrl"`
	IsActive    bool      `json:"isActive"`
	SortOrder   int       `json:"sortOrder"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`

	// 扩展字段
	BenefitValue *string `json:"benefitValue,omitempty"`
	BenefitLimit *int    `json:"benefitLimit,omitempty"`
}
