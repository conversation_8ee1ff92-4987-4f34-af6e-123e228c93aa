package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type Member struct {
	vo.GinBaseVo
	Mobile      string `json:"mobile"`
	Avatar      string `json:"avatar"`
	Salt        string `json:"salt"`          // 密码盐
	AgentCalls  int    `json:"agent_calls"`   // 剩余智能体创建次数
	Status      bool   `json:"status"`        // 当前状态
	LastLoginAt int64  `json:"last_login_at"` // 最后登录时间
	LastLoginIp string `json:"last_login_ip"` // 最后登录 IP
	Types       int    `json:"types"`         //0=用户 1=配送员
}
