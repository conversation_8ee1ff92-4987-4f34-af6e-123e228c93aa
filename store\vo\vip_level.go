package vo

import (
	"time"
)

// VipLevelVO VIP等级VO
type VipLevelVO struct {
	Id             uint64    `json:"id"`
	LevelName      string    `json:"levelName"`
	LevelCode      string    `json:"levelCode"`
	LevelRank      int       `json:"levelRank"`
	Price          float64   `json:"price"`
	DurationMonths int       `json:"durationMonths"`
	Description    *string   `json:"description"`
	IconUrl        *string   `json:"iconUrl"`
	Color          *string   `json:"color"`
	IsActive       bool      `json:"isActive"`
	SortOrder      int       `json:"sortOrder"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`

	// 扩展字段
	Benefits []VipBenefitVO `json:"benefits,omitempty"`
}
