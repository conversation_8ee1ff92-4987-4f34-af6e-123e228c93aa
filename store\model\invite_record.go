package model

import (
	"time"
)

// InviteRecord 邀请记录表
type InviteRecord struct {
	Id           uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	CreatedAt    time.Time `gorm:"column:created_at;not null" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at;not null" json:"updatedAt"`
	InviterId    uint      `gorm:"column:inviter_id;not null;index" json:"inviterId"`  // 邀请人ID（tb_members.id）
	InviteeId    *uint     `gorm:"column:invitee_id;index" json:"inviteeId"`           // 被邀请人ID（tb_members.id），预邀请阶段为空，绑定后回填
	RewardPoints int       `gorm:"column:reward_points;default:0" json:"rewardPoints"` // 奖励积分
	RewardStatus int       `gorm:"column:reward_status;default:0" json:"rewardStatus"` // 奖励状态：0=待发放 1=已发放 2=失败/撤销
	Campaign     *string   `gorm:"column:campaign;size:45" json:"campaign"`            // 活动标识/推广活动
	Channel      *string   `gorm:"column:channel;size:45" json:"channel"`              // 来源渠道（如 qr/link/timeline 等）
}

func (InviteRecord) TableName() string {
	return "tb_invite_records"
}
