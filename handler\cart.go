package handler

import (
	"haha/store/model"
	"haha/store/vo"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CartHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

func NewCartHandler(app *core.AppServer, db *gorm.DB) *CartHandler {
	handler := &CartHandler{db: db}
	handler.App = app
	return handler
}

// AddToCart 添加商品到购物车
func (h *CartHandler) AddToCart(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取请求参数
	var data struct {
		SpuId    int `json:"spu_id" binding:"required"`
		Quantity int `json:"quantity"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	if data.Quantity <= 0 {
		data.Quantity = 1
	}

	// 检查商品是否存在
	var spu model.Spu
	if err := h.db.Where("id = ?", data.SpuId).First(&spu).Error; err != nil {
		resp.ERROR(c, "商品不存在")
		return
	}

	// 检查库存
	if spu.StockNum < data.Quantity {
		resp.ERROR(c, "库存不足")
		return
	}

	// 检查购物车中是否已存在该商品
	var existingCart model.Cart
	result := h.db.Where("user_id = ? AND spu_id = ?", userID, data.SpuId).First(&existingCart)

	if result.Error == nil {
		// 商品已存在，更新数量
		newQuantity := existingCart.Quantity + data.Quantity
		if spu.StockNum < newQuantity {
			resp.ERROR(c, "库存不足")
			return
		}

		if err := h.db.Model(&existingCart).Updates(map[string]interface{}{
			"quantity": newQuantity,
			"price":    spu.Price, // 更新为当前价格
		}).Error; err != nil {
			resp.ERROR(c, "更新购物车失败")
			return
		}
	} else {
		// 新增商品到购物车
		cart := model.Cart{
			UserId:   userID,
			SpuId:    data.SpuId,
			Quantity: data.Quantity,
			Price:    spu.Price,
			Selected: true,
		}
		if err := h.db.Create(&cart).Error; err != nil {
			resp.ERROR(c, "添加到购物车失败")
			return
		}
	}

	resp.SUCCESS(c, gin.H{"message": "添加成功"})
}

// GetCartList 获取购物车列表
func (h *CartHandler) GetCartList(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 分页参数
	page := h.GetInt(c, "page", 1)
	pageSize := h.GetInt(c, "page_size", 20)
	offset := (page - 1) * pageSize

	// 查询购物车列表（关联商品信息）
	var cartItems []struct {
		model.Cart
		SpuName  string  `json:"spu_name"`
		SpuPhoto string  `json:"spu_photo"`
		SpuPrice float64 `json:"spu_price"`
		StockNum int     `json:"stock_num"`
	}

	query := h.db.Table("tb_carts c").
		Select("c.*, s.name as spu_name, s.photo as spu_photo, s.price as spu_price, s.stock_num").
		Joins("JOIN tb_spus s ON c.spu_id = s.id").
		Where("c.user_id = ?", userID).
		Order("c.created_at DESC")

	var total int64
	query.Count(&total)

	if err := query.Offset(offset).Limit(pageSize).Find(&cartItems).Error; err != nil {
		resp.ERROR(c, "获取购物车失败")
		return
	}

	// 转换为VO
	var list []vo.Cart
	for _, item := range cartItems {
		cartVo := vo.Cart{
			UserId:   item.UserId,
			SpuId:    item.SpuId,
			Quantity: item.Quantity,
			Price:    item.Price,
			Selected: item.Selected,
			SpuName:  item.SpuName,
			SpuPhoto: item.SpuPhoto,
			SpuPrice: item.SpuPrice,
			StockNum: item.StockNum,
		}
		cartVo.Id = item.Id
		cartVo.CreatedAt = item.CreatedAt.Unix()
		cartVo.UpdatedAt = item.UpdatedAt.Unix()
		list = append(list, cartVo)
	}

	resp.SUCCESS(c, baseVo.NewPage(total, page, pageSize, list))
}

// UpdateCartItem 更新购物车商品（数量、选中状态）
func (h *CartHandler) UpdateCartItem(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取购物车项ID
	cartID := h.GetTrim(c, "id")
	if cartID == "" {
		resp.ERROR(c, "购物车项ID不能为空")
		return
	}

	// 获取请求参数
	var data struct {
		Quantity *int  `json:"quantity"`
		Selected *bool `json:"selected"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	// 查询购物车项
	var cart model.Cart
	if err := h.db.Where("id = ? AND user_id = ?", cartID, userID).First(&cart).Error; err != nil {
		resp.ERROR(c, "购物车项不存在")
		return
	}

	// 准备更新数据
	updateData := make(map[string]interface{})

	// 更新数量
	if data.Quantity != nil {
		if *data.Quantity <= 0 {
			resp.ERROR(c, "数量必须大于0")
			return
		}

		// 检查库存
		var spu model.Spu
		if err := h.db.Where("id = ?", cart.SpuId).First(&spu).Error; err != nil {
			resp.ERROR(c, "商品不存在")
			return
		}

		if spu.StockNum < *data.Quantity {
			resp.ERROR(c, "库存不足")
			return
		}

		updateData["quantity"] = *data.Quantity
	}

	// 更新选中状态
	if data.Selected != nil {
		updateData["selected"] = *data.Selected
	}

	// 执行更新
	if len(updateData) > 0 {
		if err := h.db.Model(&cart).Updates(updateData).Error; err != nil {
			resp.ERROR(c, "更新购物车失败")
			return
		}
	}

	resp.SUCCESS(c, gin.H{"message": "更新成功"})
}

// RemoveFromCart 从购物车移除商品
func (h *CartHandler) RemoveFromCart(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取购物车项ID（支持单个或多个）
	var data struct {
		Ids []string `json:"ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		// 尝试获取单个ID
		cartID := h.GetTrim(c, "id")
		if cartID == "" {
			resp.ERROR(c, "请提供要删除的购物车项ID")
			return
		}
		data.Ids = []string{cartID}
	}

	// 批量删除
	if err := h.db.Where("id IN ? AND user_id = ?", data.Ids, userID).Delete(&model.Cart{}).Error; err != nil {
		resp.ERROR(c, "删除失败")
		return
	}

	resp.SUCCESS(c, gin.H{"message": "删除成功"})
}

// GetCartSummary 获取购物车汇总信息
func (h *CartHandler) GetCartSummary(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 统计购物车信息
	var summary struct {
		TotalItems    int64   `json:"total_items"`
		SelectedItems int64   `json:"selected_items"`
		TotalPrice    float64 `json:"total_price"`
	}

	// 统计总商品数量
	h.db.Model(&model.Cart{}).Where("user_id = ?", userID).Count(&summary.TotalItems)

	// 统计选中商品数量和总价
	h.db.Model(&model.Cart{}).
		Where("user_id = ? AND selected = ?", userID, true).
		Count(&summary.SelectedItems)

	// 计算选中商品总价
	h.db.Table("tb_carts").
		Select("COALESCE(SUM(quantity * price), 0)").
		Where("user_id = ? AND selected = ?", userID, true).
		Scan(&summary.TotalPrice)

	cartSummary := vo.CartSummary{
		TotalItems:    int(summary.TotalItems),
		SelectedItems: int(summary.SelectedItems),
		TotalPrice:    summary.TotalPrice,
	}

	resp.SUCCESS(c, cartSummary)
}

// ClearCart 清空购物车
func (h *CartHandler) ClearCart(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 删除用户的所有购物车项
	if err := h.db.Where("user_id = ?", userID).Delete(&model.Cart{}).Error; err != nil {
		resp.ERROR(c, "清空购物车失败")
		return
	}

	resp.SUCCESS(c, gin.H{"message": "购物车已清空"})
}

// SelectAll 全选/取消全选
func (h *CartHandler) SelectAll(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取请求参数
	var data struct {
		Selected bool `json:"selected"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	// 更新所有购物车项的选中状态
	if err := h.db.Model(&model.Cart{}).
		Where("user_id = ?", userID).
		Update("selected", data.Selected).Error; err != nil {
		resp.ERROR(c, "操作失败")
		return
	}

	msg := "已全选"
	if !data.Selected {
		msg = "已取消全选"
	}

	resp.SUCCESS(c, gin.H{"message": msg})
}
