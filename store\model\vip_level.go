package model

import (
	"time"
)

// VipLevel VIP等级表
type VipLevel struct {
	Id             uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	LevelName      string    `gorm:"column:level_name;not null" json:"levelName"`
	LevelCode      string    `gorm:"column:level_code;not null;uniqueIndex" json:"levelCode"`
	LevelRank      int       `gorm:"column:level_rank;not null" json:"levelRank"`
	Price          float64   `gorm:"column:price;type:decimal(10,2);not null" json:"price"`
	DurationMonths int       `gorm:"column:duration_months;not null" json:"durationMonths"`
	Description    *string   `gorm:"column:description;type:text" json:"description"`
	IconUrl        *string   `gorm:"column:icon_url" json:"iconUrl"`
	Color          *string   `gorm:"column:color" json:"color"`
	IsActive       bool      `gorm:"column:is_active;default:1" json:"isActive"`
	SortOrder      int       `gorm:"column:sort_order;default:0" json:"sortOrder"`
	CreatedAt      time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt      time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updatedAt"`
}

func (VipLevel) TableName() string {
	return "tb_vip_levels"
}
