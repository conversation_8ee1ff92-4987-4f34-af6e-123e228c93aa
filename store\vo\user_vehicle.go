package vo

import (
	"time"

	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type UserVehicle struct {
	vo.GinBaseVo
	OrganizationId          *uint64    `json:"organizationId,omitempty"`
	UserId                  uint64     `json:"userId"`
	LicensePlate            string     `json:"licensePlate"`
	VehicleBrand            string     `json:"vehicleBrand"`
	VehicleModel            string     `json:"vehicleModel"`
	VehicleYear             *int       `json:"vehicleYear,omitempty"`
	VehicleColor            string     `json:"vehicleColor,omitempty"`
	EngineNumber            string     `json:"engineNumber,omitempty"`
	VinCode                 string     `json:"vinCode,omitempty"`
	FuelType                string     `json:"fuelType,omitempty"`
	Transmission            string     `json:"transmission,omitempty"`
	Displacement            string     `json:"displacement,omitempty"`
	SeatCount               *int       `json:"seatCount,omitempty"`
	VehicleConfig           string     `json:"vehicleConfig,omitempty"`
	InsuranceCompany        string     `json:"insuranceCompany,omitempty"`
	InsurancePolicyNo       string     `json:"insurancePolicyNo,omitempty"`
	InsuranceStartDate      *time.Time `json:"insuranceStartDate,omitempty"`
	InsuranceEndDate        *time.Time `json:"insuranceEndDate,omitempty"`
	InsuranceAmount         *float64   `json:"insuranceAmount,omitempty"`
	InsuranceType           string     `json:"insuranceType,omitempty"`
	CurrentMileage          int        `json:"currentMileage"`
	PurchaseDate            *time.Time `json:"purchaseDate,omitempty"`
	PurchasePrice           *float64   `json:"purchasePrice,omitempty"`
	LastMaintenanceDate     *time.Time `json:"lastMaintenanceDate,omitempty"`
	LastMaintenanceMileage  *int       `json:"lastMaintenanceMileage,omitempty"`
	NextMaintenanceMileage  *int       `json:"nextMaintenanceMileage,omitempty"`
	VehicleImages           string     `json:"vehicleImages,omitempty"`
	DrivingLicenseFront     string     `json:"drivingLicenseFront,omitempty"`
	DrivingLicenseBack      string     `json:"drivingLicenseBack,omitempty"`
	RegistrationCertificate string     `json:"registrationCertificate,omitempty"`
	Status                  int        `json:"status"`
	IsDefault               int        `json:"isDefault"`
	Remark                  string     `json:"remark,omitempty"`
	Tags                    string     `json:"tags,omitempty"`
}

// AddVehicleRequest 添加车辆请求
type AddVehicleRequest struct {
	LicensePlate            string     `json:"licensePlate" binding:"required"` // 必填
	VehicleBrand            string     `json:"vehicleBrand" binding:"required"` // 必填
	VehicleModel            string     `json:"vehicleModel" binding:"required"` // 必填
	VehicleYear             *int       `json:"vehicleYear,omitempty"`
	VehicleColor            string     `json:"vehicleColor,omitempty"`
	EngineNumber            string     `json:"engineNumber,omitempty"`
	VinCode                 string     `json:"vinCode,omitempty"`
	FuelType                string     `json:"fuelType,omitempty"`
	Transmission            string     `json:"transmission,omitempty"`
	Displacement            string     `json:"displacement,omitempty"`
	SeatCount               *int       `json:"seatCount,omitempty"`
	VehicleConfig           string     `json:"vehicleConfig,omitempty"`
	InsuranceCompany        string     `json:"insuranceCompany,omitempty"`
	InsurancePolicyNo       string     `json:"insurancePolicyNo,omitempty"`
	InsuranceStartDate      *time.Time `json:"insuranceStartDate,omitempty"`
	InsuranceEndDate        *time.Time `json:"insuranceEndDate,omitempty"`
	InsuranceAmount         *float64   `json:"insuranceAmount,omitempty"`
	InsuranceType           string     `json:"insuranceType,omitempty"`
	CurrentMileage          int        `json:"currentMileage,omitempty"`
	PurchaseDate            *time.Time `json:"purchaseDate,omitempty"`
	PurchasePrice           *float64   `json:"purchasePrice,omitempty"`
	LastMaintenanceDate     *time.Time `json:"lastMaintenanceDate,omitempty"`
	LastMaintenanceMileage  *int       `json:"lastMaintenanceMileage,omitempty"`
	NextMaintenanceMileage  *int       `json:"nextMaintenanceMileage,omitempty"`
	VehicleImages           string     `json:"vehicleImages,omitempty"`
	DrivingLicenseFront     string     `json:"drivingLicenseFront,omitempty"`
	DrivingLicenseBack      string     `json:"drivingLicenseBack,omitempty"`
	RegistrationCertificate string     `json:"registrationCertificate,omitempty"`
	IsDefault               int        `json:"isDefault,omitempty"` // 是否设为默认车辆
	Remark                  string     `json:"remark,omitempty"`
	Tags                    string     `json:"tags,omitempty"`
}

// UpdateVehicleRequest 更新车辆请求
type UpdateVehicleRequest struct {
	InsuranceCompany    string `json:"insurance_company,omitempty"`
	InsurancePolicyNo   string `json:"insurance_policy_no,omitempty"`
	InsuranceEndDate    string `json:"insurance_end_date,omitempty"`
	CurrentMileage      int    `json:"current_mileage,omitempty"`
	LastMaintenanceDate string `json:"last_maintenance_date,omitempty"`
}

// VehicleListRequest 车辆列表请求参数
type VehicleListRequest struct {
	Page      int    `json:"page" form:"page"`
	PageSize  int    `json:"page_size" form:"page_size"`
	Status    *int   `json:"status" form:"status"`         // 状态筛选
	Query     string `json:"query" form:"query"`           // 搜索关键词（车牌号、品牌、车型）
	Sort      string `json:"sort" form:"sort"`             // 排序方式
	IsDefault *int   `json:"is_default" form:"is_default"` // 是否默认车辆
}

// UserVehicleDetailResponse 车辆详情响应
type UserVehicleDetailResponse struct {
	UserVehicle
	InsuranceExpireDays *int `json:"insuranceExpireDays,omitempty"` // 保险到期剩余天数
	MaintenanceOverdue  bool `json:"maintenanceOverdue"`            // 是否需要保养
	IsInsuranceExpiring bool `json:"isInsuranceExpiring"`           // 保险是否即将到期（30天内）
}
