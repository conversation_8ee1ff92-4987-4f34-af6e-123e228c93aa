Listen = "0.0.0.0:1234"
MysqlDns = "root:Mcfee123321!@tcp(***************:3306)/db_haha?charset=utf8mb4&parseTime=True&loc=Local"
LogLevel = "info"
ProxyURL = ""
WeiXin = true
CommonQuery = true

[Session]
  Type = 0
  SecretKey = "BB3647441FFA4B5DB4E64A29B53CE525"
  UserSecretKey = ""
  MaxAge = 86400
  [Session.Secret]
    Issuer = "haha"
    Audience = "haha.owner"

[Redis]
  Host = "***************"
  Port = 6379
  Password = "Mcfee123321"
  DB = 33

[OSS]
  Active = "aliyun"
  [OSS.Local]
    BasePath = ""
    BaseURL = ""
  [OSS.AliYun]
    Endpoint = "oss-cn-shenzhen.aliyuncs.com"
    AccessKey = "LTAIqJ3g6vpDTwF6"
    AccessSecret = "jeR78IIz9YOydoAQJwFelb2DaJHGfK"
    Bucket = "mcfee"
    SubDir = "ztl"
    Domain = "https://oss.csdu.net"

[WxMini]
  Appid = "wx68c41737659e2ead"
  AppSecret = "a1ba6eee444cf75160e4ed3ace90b20b"

[Geetest]
  Active = false

[Douyin]
  Active = false
  Key = ""
  Secret = ""

[Coze]
  Active = false
  ClientType = ""
  ClientID = ""
  PrivateKey = ""
  PublicKeyID = ""
  PrivateKeyFilePath = ""
  WwwBase = ""
  ApiBase = ""
  PersonalToken = ""

[HuoShan]
  ApiKey = ""

[ComfyUi]
  Url = ""

[WebSocket]
  Url = ""

[NSQ]
  Active = true
  NsqAddr = "127.0.0.1:4150"
  NsqLookupdAddr = "127.0.0.1:4161"
  SupportedTopics = ["haha_spu_order"]

[BaiduImage]
  active = false
  api_key = ""
  secret_key = ""
  timeout = 0

[TencentMap]
  Active = true
  Key = "LJLBZ-EZF3Q-LZ75G-27TM4-5CUQE-EABBD"

[ApihzConfig]
  [ApihzConfig.Base]
    Active = false
    AppId = ""
    AppKey = ""
    GetHostUrl = ""
    DefaultHostUrl = ""
  [ApihzConfig.CaiJi]
    Path = ""
  [ApihzConfig.YuBao]
    Path = ""

[WxPay]
  Active = false
  AppID = "wx68c41737659e2ead"
  MchID = ""
  SerialNo = "526F93A59D136DF550C3B9247C65051EEC0B68B8"
  APIv3Key = "Zitailang20250707qichebaoyang888"
  PrivateKeyPath = "./apiclient_key.pem"
  NotifyURL = "http://127.0.0.1:1234"
  Debug = true

[OAuth2]
  Active = false
  RedirectURL = ""
  StateSecret = ""
  TokenStorage = ""

[OAuthServer]
  Active = false
  ServerName = ""
  IssuerURL = ""
  AuthorizeURL = ""
  TokenURL = ""
  UserInfoURL = ""
  AuthCodeTimeout = 0
  AccessTokenTTL = 0
  RefreshTokenTTL = 0
  RequireClientAuth = false
