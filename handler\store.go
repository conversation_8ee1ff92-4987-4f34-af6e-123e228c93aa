package handler

import (
	"errors"
	"fmt"
	"haha/store/model"
	"haha/store/vo"
	"math"
	"strconv"
	"strings"
	"time"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type StoreHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

func NewStoreHandler(app *core.AppServer, db *gorm.DB) *StoreHandler {
	h := StoreHandler{
		db: db,
	}
	h.App = app
	return &h
}

// List 获取门店列表
func (h *StoreHandler) List(c *gin.Context) {
	var req vo.StoreListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	var total int64
	var items []model.Store
	var list = make([]vo.StoreDetailResponse, 0)

	session := h.db.Session(&gorm.Session{})
	session = session.Model(&model.Store{})

	// 城市筛选
	if req.City != "" {
		session = session.Where("city = ?", req.City)
	}

	// 区县筛选
	if req.District != "" {
		session = session.Where("district = ?", req.District)
	}

	// 状态筛选
	if req.Status != nil {
		session = session.Where("status = ?", *req.Status)
	}

	// 推荐筛选
	if req.IsFeatured != nil {
		session = session.Where("is_featured = ?", *req.IsFeatured)
	}

	// 搜索条件
	if req.Query != "" {
		session = session.Where("store_name LIKE ? OR detailed_address LIKE ? OR contact_person LIKE ?",
			fmt.Sprintf("%%%s%%", req.Query),
			fmt.Sprintf("%%%s%%", req.Query),
			fmt.Sprintf("%%%s%%", req.Query))
	}

	// 半径筛选（如果提供了经纬度和半径）
	if req.Longitude != 0 && req.Latitude != 0 && req.Radius > 0 {
		// 使用简单的矩形范围筛选，提高查询性能
		latRange := req.Radius / 111.0 // 1纬度约111km
		lngRange := req.Radius / (111.0 * math.Cos(req.Latitude*math.Pi/180))

		session = session.Where("latitude BETWEEN ? AND ? AND longitude BETWEEN ? AND ?",
			req.Latitude-latRange, req.Latitude+latRange,
			req.Longitude-lngRange, req.Longitude+lngRange)
	}

	// 获取总数
	session.Count(&total)

	// 排序
	orderBy := "sort desc, id desc"
	if req.Sort == "distance_asc" && req.Longitude != 0 && req.Latitude != 0 {
		// 如果是距离排序，先按范围查询，再在程序中计算距离排序
		orderBy = "sort desc, id desc"
	}

	// 查询数据
	res := session.Order(orderBy).Offset(offset).Limit(req.PageSize).Find(&items)
	if res.Error == nil {
		for _, item := range items {
			var storeVo vo.StoreDetailResponse
			err := utils.CopyObject(item, &storeVo.Store)
			if err == nil {
				storeVo.Id = item.Id
				storeVo.CreatedAt = item.CreatedAt.Unix()
				storeVo.UpdatedAt = item.UpdatedAt.Unix()

				// 计算距离
				if req.Longitude != 0 && req.Latitude != 0 {
					storeVo.Distance = h.calculateDistance(req.Latitude, req.Longitude, item.Latitude, item.Longitude)
				}

				// 判断是否营业
				storeVo.IsOpen = h.isStoreOpen(item.BusinessHours, item.Status)

				// 格式化地址
				storeVo.FormattedAddress = h.formatAddress(item.Province, item.City, item.District, item.Street, item.DetailedAddress)

				list = append(list, storeVo)
			} else {
				fmt.Println(err)
			}
		}

		// 如果是距离排序，在程序中排序
		if req.Sort == "distance_asc" && req.Longitude != 0 && req.Latitude != 0 {
			for i := 0; i < len(list)-1; i++ {
				for j := i + 1; j < len(list); j++ {
					if list[i].Distance > list[j].Distance {
						list[i], list[j] = list[j], list[i]
					}
				}
			}
		}
	}

	resp.SUCCESS(c, baseVo.NewPage(total, req.Page, req.PageSize, list))
}

// Get 获取门店详情
func (h *StoreHandler) Get(c *gin.Context) {
	idStr := c.Query("id")
	if idStr == "" {
		resp.ERROR(c, "门店ID不能为空")
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		resp.ERROR(c, "门店ID格式错误")
		return
	}

	// 获取用户位置（可选）
	latStr := c.Query("latitude")
	lngStr := c.Query("longitude")
	var userLat, userLng float64

	if latStr != "" && lngStr != "" {
		if lat, err := strconv.ParseFloat(latStr, 64); err == nil {
			userLat = lat
		}
		if lng, err := strconv.ParseFloat(lngStr, 64); err == nil {
			userLng = lng
		}
	}

	var store model.Store
	result := h.db.Where("id = ?", id).First(&store)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			resp.ERROR(c, "门店不存在")
		} else {
			resp.ERROR(c, "获取门店信息失败")
		}
		return
	}

	var storeVo vo.StoreDetailResponse
	err = utils.CopyObject(store, &storeVo.Store)
	if err != nil {
		resp.ERROR(c, "数据转换失败")
		return
	}

	storeVo.Id = store.Id
	storeVo.CreatedAt = store.CreatedAt.Unix()
	storeVo.UpdatedAt = store.UpdatedAt.Unix()

	// 计算距离
	if userLat != 0 && userLng != 0 {
		storeVo.Distance = h.calculateDistance(userLat, userLng, store.Latitude, store.Longitude)
	}

	// 判断是否营业
	storeVo.IsOpen = h.isStoreOpen(store.BusinessHours, store.Status)

	// 格式化地址
	storeVo.FormattedAddress = h.formatAddress(store.Province, store.City, store.District, store.Street, store.DetailedAddress)

	resp.SUCCESS(c, storeVo)
}

// GetFeatured 获取推荐门店
func (h *StoreHandler) GetFeatured(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	var items []model.Store
	var list = make([]vo.Store, 0)

	// 查询推荐门店，状态为正常营业
	res := h.db.Where("is_featured = ? AND status = ?", 1, 1).
		Order("sort desc, id desc").
		Limit(limit).
		Find(&items)

	if res.Error == nil {
		for _, item := range items {
			var storeVo vo.Store
			err := utils.CopyObject(item, &storeVo)
			if err == nil {
				storeVo.Id = item.Id
				storeVo.CreatedAt = item.CreatedAt.Unix()
				storeVo.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, storeVo)
			} else {
				fmt.Println(err)
			}
		}
	}

	resp.SUCCESS(c, list)
}

// GetNearby 获取附近门店
func (h *StoreHandler) GetNearby(c *gin.Context) {
	var req vo.StoreNearbyRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		resp.ERROR(c, "参数错误：经纬度必填")
		return
	}

	// 设置默认值
	if req.Radius <= 0 {
		req.Radius = 10 // 默认10km
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	// 使用简单的矩形范围筛选
	latRange := req.Radius / 111.0
	lngRange := req.Radius / (111.0 * math.Cos(req.Latitude*math.Pi/180))

	session := h.db.Session(&gorm.Session{})
	session = session.Model(&model.Store{})

	// 范围筛选
	session = session.Where("latitude BETWEEN ? AND ? AND longitude BETWEEN ? AND ?",
		req.Latitude-latRange, req.Latitude+latRange,
		req.Longitude-lngRange, req.Longitude+lngRange)

	// 状态筛选
	if req.Status != nil {
		session = session.Where("status = ?", *req.Status)
	} else {
		session = session.Where("status = ?", 1) // 默认只显示正常营业的
	}

	var items []model.Store
	var list = make([]vo.StoreDetailResponse, 0)

	res := session.Order("sort desc, id desc").Limit(req.Limit).Find(&items)
	if res.Error == nil {
		for _, item := range items {
			// 精确计算距离
			distance := h.calculateDistance(req.Latitude, req.Longitude, item.Latitude, item.Longitude)
			if distance <= req.Radius {
				var storeVo vo.StoreDetailResponse
				err := utils.CopyObject(item, &storeVo.Store)
				if err == nil {
					storeVo.Id = item.Id
					storeVo.CreatedAt = item.CreatedAt.Unix()
					storeVo.UpdatedAt = item.UpdatedAt.Unix()
					storeVo.Distance = distance
					storeVo.IsOpen = h.isStoreOpen(item.BusinessHours, item.Status)
					storeVo.FormattedAddress = h.formatAddress(item.Province, item.City, item.District, item.Street, item.DetailedAddress)
					list = append(list, storeVo)
				}
			}
		}

		// 按距离排序
		for i := 0; i < len(list)-1; i++ {
			for j := i + 1; j < len(list); j++ {
				if list[i].Distance > list[j].Distance {
					list[i], list[j] = list[j], list[i]
				}
			}
		}
	}

	resp.SUCCESS(c, list)
}

// GetCities 获取门店城市列表
func (h *StoreHandler) GetCities(c *gin.Context) {
	var cities []vo.CityStoreCount

	res := h.db.Model(&model.Store{}).
		Select("city, COUNT(*) as count").
		Where("city != '' AND status = ?", 1).
		Group("city").
		Order("count desc").
		Find(&cities)

	if res.Error != nil {
		resp.ERROR(c, "获取城市列表失败")
		return
	}

	resp.SUCCESS(c, cities)
}

// Search 搜索门店
func (h *StoreHandler) Search(c *gin.Context) {
	keyword := strings.TrimSpace(c.Query("keyword"))
	if keyword == "" {
		resp.ERROR(c, "搜索关键词不能为空")
		return
	}

	page := h.GetInt(c, "page", 1)
	pageSize := h.GetInt(c, "page_size", 20)
	offset := (page - 1) * pageSize

	var total int64
	var items []model.Store
	var list = make([]vo.Store, 0)

	session := h.db.Session(&gorm.Session{})
	session = session.Model(&model.Store{}).Where("status = ?", 1)

	// 搜索门店名称、详细地址、联系人
	session = session.Where("store_name LIKE ? OR detailed_address LIKE ? OR contact_person LIKE ?",
		fmt.Sprintf("%%%s%%", keyword),
		fmt.Sprintf("%%%s%%", keyword),
		fmt.Sprintf("%%%s%%", keyword))

	session.Count(&total)

	res := session.Order("sort desc, id desc").
		Offset(offset).
		Limit(pageSize).
		Find(&items)

	if res.Error == nil {
		for _, item := range items {
			var storeVo vo.Store
			err := utils.CopyObject(item, &storeVo)
			if err == nil {
				storeVo.Id = item.Id
				storeVo.CreatedAt = item.CreatedAt.Unix()
				storeVo.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, storeVo)
			} else {
				fmt.Println(err)
			}
		}
	}

	resp.SUCCESS(c, baseVo.NewPage(total, page, pageSize, list))
}

// GetStoresBySpu 根据商品ID查询门店列表
func (h *StoreHandler) GetStoresBySpu(c *gin.Context) {
	var req vo.StoreBySpuRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		fmt.Println(err)
		resp.ERROR(c, "参数错误")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	// 先查询商品信息
	var spu model.Spu
	if err := h.db.Where("id = ?", req.SpuId).First(&spu).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.ERROR(c, "商品不存在")
		} else {
			resp.ERROR(c, "获取商品信息失败")
		}
		return
	}

	// 构建商品基本信息
	spuInfo := vo.SpuInfo{
		SpuId:         spu.Id,
		SpuName:       spu.Name,
		SpuPhoto:      spu.Photo,
		OriginalPrice: spu.Price,
		OriginalStock: spu.StockNum,
	}

	var total int64
	var items []model.StoreSpu
	var list = make([]vo.StoreWithSpuResponse, 0)

	session := h.db.Session(&gorm.Session{})

	// 查询所有销售该商品的门店（包括总店和分店）
	// 只查询已上架的门店商品关联
	session = session.Model(&model.StoreSpu{}).Where("spu_id = ? AND is_active = ?", req.SpuId, 1)

	// 预加载门店信息
	session = session.Preload("Store")

	// 城市筛选
	if req.City != "" {
		session = session.Joins("JOIN tb_stores ON tb_store_spus.store_id = tb_stores.id").
			Where("tb_stores.city = ?", req.City)
	}

	// 区县筛选
	if req.District != "" {
		session = session.Joins("JOIN tb_stores ON tb_store_spus.store_id = tb_stores.id").
			Where("tb_stores.district = ?", req.District)
	}

	// 状态筛选
	if req.Status != nil {
		session = session.Joins("JOIN tb_stores ON tb_store_spus.store_id = tb_stores.id").
			Where("tb_stores.status = ?", *req.Status)
	}

	// 半径筛选（如果提供了经纬度和半径）
	// 使用矩形范围进行初步筛选，提高查询性能
	if req.Longitude != 0 && req.Latitude != 0 && req.Radius > 0 {
		latRange := req.Radius / 111.0
		lngRange := req.Radius / (111.0 * math.Cos(req.Latitude*math.Pi/180))

		session = session.Joins("JOIN tb_stores ON tb_store_spus.store_id = tb_stores.id").
			Where("tb_stores.latitude BETWEEN ? AND ? AND tb_stores.longitude BETWEEN ? AND ?",
				req.Latitude-latRange, req.Latitude+latRange,
				req.Longitude-lngRange, req.Longitude+lngRange)
	}

	// 获取总数
	session.Count(&total)

	// 排序
	orderBy := "sort_order desc, id desc"
	if req.Sort == "distance_asc" && req.Longitude != 0 && req.Latitude != 0 {
		orderBy = "sort_order desc, id desc"
	}

	// 查询数据
	res := session.Order(orderBy).Offset(offset).Limit(req.PageSize).Find(&items)
	if res.Error == nil {
		for _, item := range items {
			var storeWithSpu vo.StoreWithSpuResponse

			// 复制门店信息
			err := utils.CopyObject(item.Store, &storeWithSpu.Store)
			if err == nil {
				storeWithSpu.Id = item.Store.Id
				storeWithSpu.CreatedAt = item.Store.CreatedAt.Unix()
				storeWithSpu.UpdatedAt = item.Store.UpdatedAt.Unix()

				// 计算距离
				if req.Longitude != 0 && req.Latitude != 0 {
					storeWithSpu.Distance = h.calculateDistance(req.Latitude, req.Longitude, item.Store.Latitude, item.Store.Longitude)
				}

				// 判断是否营业
				storeWithSpu.IsOpen = h.isStoreOpen(item.Store.BusinessHours, item.Store.Status)

				// 格式化地址
				storeWithSpu.FormattedAddress = h.formatAddress(item.Store.Province, item.Store.City, item.Store.District, item.Store.Street, item.Store.DetailedAddress)

				// 设置商品信息
				storeWithSpu.SpuInfo = spuInfo

				// 设置门店价格和库存（直接从tb_store_spus表获取）
				storeWithSpu.StorePrice = item.StorePrice
				storeWithSpu.StoreStock = item.StoreStockNum

				// 设置其他信息
				storeWithSpu.IsActive = item.IsActive
				storeWithSpu.StoreSalesNum = item.StoreSalesNum

				list = append(list, storeWithSpu)
			} else {
				fmt.Println(err)
			}
		}

		// 如果是距离排序，在程序中排序
		if req.Sort == "distance_asc" && req.Longitude != 0 && req.Latitude != 0 {
			for i := 0; i < len(list)-1; i++ {
				for j := i + 1; j < len(list); j++ {
					if list[i].Distance > list[j].Distance {
						list[i], list[j] = list[j], list[i]
					}
				}
			}
		}

		// 如果设置了半径筛选，过滤掉超出半径的门店
		if req.Longitude != 0 && req.Latitude != 0 && req.Radius > 0 {
			filteredList := make([]vo.StoreWithSpuResponse, 0)
			for _, item := range list {
				if item.Distance <= req.Radius {
					filteredList = append(filteredList, item)
				}
			}
			list = filteredList
			// 重新计算总数（这里简化处理，实际项目中可能需要更精确的分页处理）
			total = int64(len(list))
		}
	}

	resp.SUCCESS(c, baseVo.NewPage(total, req.Page, req.PageSize, list))
}

// calculateDistance 计算两点间距离（km）
func (h *StoreHandler) calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const R = 6371 // 地球半径（km）

	dLat := (lat2 - lat1) * math.Pi / 180
	dLng := (lng2 - lng1) * math.Pi / 180

	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1*math.Pi/180)*math.Cos(lat2*math.Pi/180)*
			math.Sin(dLng/2)*math.Sin(dLng/2)

	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return R * c
}

// isStoreOpen 判断门店是否营业
func (h *StoreHandler) isStoreOpen(businessHours string, status int) bool {
	// 如果状态不是正常营业，直接返回false
	if status != 1 {
		return false
	}

	// 简单实现：这里可以根据businessHours JSON解析具体的营业时间
	// 当前实现只判断状态
	now := time.Now()
	hour := now.Hour()

	// 假设营业时间是9:00-22:00
	return hour >= 9 && hour < 22
}

// formatAddress 格式化地址
func (h *StoreHandler) formatAddress(province, city, district, street, detailed string) string {
	var parts []string

	if province != "" {
		parts = append(parts, province)
	}
	if city != "" && city != province {
		parts = append(parts, city)
	}
	if district != "" {
		parts = append(parts, district)
	}
	if street != "" {
		parts = append(parts, street)
	}
	if detailed != "" {
		parts = append(parts, detailed)
	}

	return strings.Join(parts, "")
}
