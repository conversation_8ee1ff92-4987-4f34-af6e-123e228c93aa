package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type TimeCard struct {
	vo.GinBaseVo
	Name          string  `json:"name"`
	Description   string  `json:"description"`
	Photo         string  `json:"photo"`
	TotalTimes    int64   `json:"totalTimes"`
	ValidityDays  int64   `json:"validityDays"`
	OriginalPrice float64 `json:"originalPrice"`
	DiscountPrice float64 `json:"discountPrice"`
	Status        int64   `json:"status"`
	Sort          int64   `json:"sort"`
	SalesNum      int64   `json:"salesNum"`
}

// TimeCardListRequest 次卡列表请求参数
type TimeCardListRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	Status   *int64 `json:"status" form:"status"` // 状态筛选
	Query    string `json:"query" form:"query"`   // 搜索关键词
	Sort     string `json:"sort" form:"sort"`     // 排序方式：sort_asc, sort_desc, sales_desc, price_asc, price_desc
}

// TimeCardDetailResponse 次卡详情响应
type TimeCardDetailResponse struct {
	TimeCard
	IsAvailable bool `json:"isAvailable"` // 是否可购买
}
