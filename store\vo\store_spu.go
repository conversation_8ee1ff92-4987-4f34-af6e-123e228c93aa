package vo

// StoreBySpuRequest 根据商品ID查询门店列表请求参数
type StoreBySpuRequest struct {
	SpuId     uint    `json:"spu_id" form:"spu_id" binding:"required"`
	Page      int     `json:"page" form:"page"`
	PageSize  int     `json:"page_size" form:"page_size"`
	City      string  `json:"city" form:"city"`           // 城市筛选
	District  string  `json:"district" form:"district"`   // 区县筛选
	Status    *int    `json:"status" form:"status"`       // 状态筛选
	Sort      string  `json:"sort" form:"sort"`           // 排序方式：sort_desc, distance_asc
	Longitude float64 `json:"longitude" form:"longitude"` // 经度（用于距离排序）
	Latitude  float64 `json:"latitude" form:"latitude"`   // 纬度（用于距离排序）
	Radius    float64 `json:"radius" form:"radius"`       // 搜索半径（km）
}

// SpuInfo 商品基本信息
type SpuInfo struct {
	SpuId         uint    `json:"spuId"`
	SpuName       string  `json:"spuName"`
	SpuPhoto      string  `json:"spuPhoto"`
	OriginalPrice float64 `json:"originalPrice"` // 商品原价
	OriginalStock int     `json:"originalStock"` // 商品原库存
}

// StoreWithSpuResponse 门店商品关联响应
type StoreWithSpuResponse struct {
	StoreDetailResponse
	SpuInfo       SpuInfo `json:"spuInfo"`       // 商品基本信息
	StorePrice    float64 `json:"storePrice"`    // 门店价格
	StoreStock    int     `json:"storeStock"`    // 门店库存
	IsActive      int     `json:"isActive"`      // 是否上架
	StoreSalesNum int     `json:"storeSalesNum"` // 门店销量
}
