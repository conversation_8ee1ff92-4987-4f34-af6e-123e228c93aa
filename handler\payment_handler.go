package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"haha/service/pay"
	"haha/store/model"
	localTypes "haha/types"
	"haha/utils"
	"io"
	"net/http"
	"sync"
	"time"

	"gitee.com/masculine_girl/ginbase/api/core"
	baseTypes "gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/service/wxchat"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

const (
	PayWayWx = "微信"

	// 重试相关常量
	MaxRetryTimes = 3           // 最大重试次数
	RetryInterval = time.Second // 重试间隔
)

type PaymentHandler struct {
	pkg.BaseHandler
	wxPayService *pay.WxPayService
	db           *gorm.DB
	lock         sync.Mutex
}

func NewPaymentHandler(server *core.AppServer, wxPayService *pay.WxPayService, db *gorm.DB) *PaymentHandler {
	h := PaymentHandler{
		wxPayService: wxPayService,
		db:           db,
		lock:         sync.Mutex{},
	}
	h.App = server
	return &h
}

func (h *PaymentHandler) DoPay(c *gin.Context) {
	ctx := context.Background()
	user, err := utils.GetLoginUser(c, h.db)
	if err != nil {
		resp.NotAuth(c)
		return
	}
	orderNo := h.GetTrim(c, "order_no")

	if orderNo == "" {
		resp.ERROR(c, baseTypes.InvalidArgs)
		return
	}

	var order model.Order
	resDb := h.db.Where("order_no = ?", orderNo).First(&order)
	if resDb.Error != nil {
		resp.ERROR(c, "订单没找到")
		return
	}

	fmt.Println("=== 支付下单序列号检查 ===")
	fmt.Printf("当前客户端序列号: %s\n", h.wxPayService.WxPayClient.Client.WxSerialNo)
	fmt.Printf("配置商户证书序列号: %s\n", h.App.Config.WxPay.SerialNo)

	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)
	params := &wxchat.JSAPIOrderRequest{
		Description: order.Subject,
		OutTradeNo:  orderNo,
		Amount:      utils.YuanToFen(order.Amount),
		OpenID:      user.OpenId,
		NotifyURL:   h.wxPayService.WxPayClient.GetNotify(),
		TimeExpire:  expire,
		Attach:      "",
		GoodsTag:    "",
	}

	res, err := h.wxPayService.JSAPIOrder(ctx, params)
	if err != nil {
		resp.ERROR(c, "支付失败: "+err.Error())
		return
	}

	resp.SUCCESS(c, res)
}

// Notify 微信支付回调通知处理
func (h *PaymentHandler) Notify(c *gin.Context) {
	// 只接受POST请求
	if c.Request.Method != http.MethodPost {
		fmt.Println("只能是POST方式", http.StatusMethodNotAllowed)
		return
	}

	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	fmt.Println(c.Request.Header)
	fmt.Println(string(body))
	if err != nil {
		fmt.Println("读取回调通知请求体失败:", err)
		return
	}
	defer c.Request.Body.Close()

	// 获取签名相关头部信息
	timestamp := c.Request.Header.Get("Wechatpay-Timestamp")
	nonce := c.Request.Header.Get("Wechatpay-Nonce")
	signature := c.Request.Header.Get("Wechatpay-Signature")
	serial := c.Request.Header.Get("Wechatpay-Serial")

	if h.wxPayService.WxPayClient.Client.WxSerialNo != serial {
		fmt.Println("微信平台序列号不一致:", err)
		return
	}

	// 构建回调通知请求
	req := &wxchat.PayNotifyRequest{
		Timestamp: timestamp,
		Nonce:     nonce,
		Signature: signature,
		Serial:    serial,
		Body:      string(body),
	}

	// 验证签名
	if err := h.wxPayService.WxPayClient.VerifyNotify(req); err != nil {
		fmt.Println("验证回调通知签名失败:", err)
		return
	}
	// 解析通知内容
	var notifyData map[string]interface{}
	if err := json.Unmarshal(body, &notifyData); err != nil {
		fmt.Println("解析回调通知内容失败:", err)
		return
	}

	// 根据事件类型处理
	eventType, _ := notifyData["event_type"].(string)

	// 先返回成功响应给微信
	writeNotifyResponse(c.Writer, h.wxPayService.WxPayClient.GetNotifySuccessResponse())

	// 异步处理业务逻辑
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("异步处理支付通知时发生panic: %v\n", r)
			}
		}()

		switch eventType {
		case "TRANSACTION.SUCCESS":
			// 处理支付成功通知
			h.handlePaymentSuccess(req, notifyData)
		case "REFUND.SUCCESS":
			// 处理退款成功通知
			h.handleRefundSuccess(req, notifyData)
		case "REFUND.ABNORMAL":
			// 处理退款异常通知
			h.handleRefundAbnormal(req, notifyData)
		case "REFUND.CLOSED":
			// 处理退款关闭通知
			h.handleRefundClosed(req, notifyData)
		default:
			fmt.Println("收到未知类型的回调通知", "EventType", eventType)
		}
	}()
}

// handlePaymentSuccess 处理支付成功通知
func (h *PaymentHandler) handlePaymentSuccess(req *wxchat.PayNotifyRequest, notifyData map[string]interface{}) {
	// 获取订单号
	resource, ok := notifyData["resource"].(map[string]interface{})
	if !ok {
		fmt.Println("无法获取resource数据")
		return
	}

	originalType, ok := resource["original_type"].(string)
	if !ok || originalType != "transaction" {
		fmt.Println("非交易类型通知")
		return
	}

	// 解密通知数据
	plaintext, err := wxchat.DecryptAES256GCM(
		string(h.wxPayService.WxPayClient.Client.ApiV3Key),
		resource["associated_data"].(string),
		resource["nonce"].(string),
		resource["ciphertext"].(string),
	)
	if err != nil {
		fmt.Println("解密回调通知数据失败:", err)
		// 即使解密失败，也返回基本的通知信息，不影响业务流程
	}

	// 解析解密后的数据
	var transactionData struct {
		OutTradeNo string `json:"out_trade_no"`
		TradeState string `json:"trade_state"`
	}

	if err := json.Unmarshal([]byte(plaintext), &transactionData); err != nil {
		fmt.Println("解析交易数据失败:", err)
		return
	}

	// 使用互斥锁保护并发操作
	h.lock.Lock()
	defer h.lock.Unlock()

	// 重试处理逻辑
	var processErr error
	for i := 0; i < MaxRetryTimes; i++ {
		if i > 0 {
			fmt.Printf("第 %d 次重试处理订单 %s\n", i+1, transactionData.OutTradeNo)
			time.Sleep(RetryInterval * time.Duration(i+1))
		}

		// 开始数据库事务
		tx := h.db.Begin()
		if tx.Error != nil {
			processErr = fmt.Errorf("开始事务失败: %v", tx.Error)
			continue
		}

		// 查询订单
		var order model.Order
		if err := tx.Where("order_no = ?", transactionData.OutTradeNo).First(&order).Error; err != nil {
			tx.Rollback()
			processErr = fmt.Errorf("查询订单失败: %v", err)
			continue
		}

		// 幂等性处理：检查订单状态
		if order.Status == localTypes.OrderPaidSuccess {
			fmt.Printf("订单 %s 已处理过，跳过处理\n", transactionData.OutTradeNo)
			tx.Rollback()
			return
		}

		// 更新订单状态为已支付
		if err := tx.Model(&order).Updates(map[string]interface{}{
			"status":   localTypes.OrderPaidSuccess,
			"pay_time": time.Now().Unix(),
			"pay_way":  PayWayWx,
		}).Error; err != nil {
			tx.Rollback()
			processErr = fmt.Errorf("更新订单状态失败: %v", err)
			continue
		}

		// 更新商品销量（次卡订单更新次卡销量，普通商品订单更新商品销量）
		if err := h.updateProductSalesOnPayment(tx, order.Id, 1); err != nil {
			tx.Rollback()
			processErr = fmt.Errorf("更新商品销量失败: %v", err)
			continue
		}

		// 提交事务
		if err := tx.Commit().Error; err != nil {
			processErr = fmt.Errorf("提交事务失败: %v", err)
			continue
		}

		// 处理成功
		fmt.Printf("订单 %s 支付成功处理完成，已更新商品销量\n", transactionData.OutTradeNo)

		// 验证订单状态是否正确更新
		var verifyOrder model.Order
		if err := h.db.Where("order_no = ?", transactionData.OutTradeNo).First(&verifyOrder).Error; err != nil {
			fmt.Printf("验证订单状态失败: %v\n", err)
		} else {
			fmt.Printf("订单状态验证: 订单号=%s, 状态=%d, 支付时间=%d, 支付方式=%s\n",
				verifyOrder.OrderNo, verifyOrder.Status, verifyOrder.PayTime, verifyOrder.PayWay)
		}
		return
	}

	// 所有重试都失败
	if processErr != nil {
		fmt.Printf("订单 %s 处理失败，已重试 %d 次，最后错误: %v\n",
			transactionData.OutTradeNo, MaxRetryTimes, processErr)
	}
}

// handleRefundSuccess 处理退款成功通知
func (h *PaymentHandler) handleRefundSuccess(req *wxchat.PayNotifyRequest, notifyData map[string]interface{}) {
	fmt.Printf("处理退款成功通知")

	// 处理退款成功的业务逻辑
	response, err := h.wxPayService.WxPayClient.HandleRefundNotify(req)
	if err != nil {
		fmt.Printf("处理退款回调失败:", err)
		return
	}

	fmt.Printf("退款回调处理成功: NotifyType=%s, EventType=%s\n",
		response.NotifyType, response.EventType)

	// 获取退款订单信息并减少销量
	resource, ok := notifyData["resource"].(map[string]interface{})
	if !ok {
		fmt.Println("无法获取退款resource数据")
		return
	}

	// 解密退款通知数据
	plaintext, err := wxchat.DecryptAES256GCM(
		string(h.wxPayService.WxPayClient.Client.ApiV3Key),
		resource["associated_data"].(string),
		resource["nonce"].(string),
		resource["ciphertext"].(string),
	)
	if err != nil {
		fmt.Printf("解密退款通知数据失败: %v\n", err)
		return
	}

	// 解析退款数据
	var refundData struct {
		OutTradeNo string `json:"out_trade_no"`
		Status     string `json:"status"`
	}

	if err := json.Unmarshal([]byte(plaintext), &refundData); err != nil {
		fmt.Printf("解析退款数据失败: %v\n", err)
		return
	}

	// 使用互斥锁保护并发操作
	h.lock.Lock()
	defer h.lock.Unlock()

	// 开始数据库事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			fmt.Printf("处理退款通知时发生panic: %v\n", r)
		}
	}()

	// 查询订单
	var order model.Order
	if err := tx.Where("order_no = ?", refundData.OutTradeNo).First(&order).Error; err != nil {
		tx.Rollback()
		fmt.Printf("查询退款订单失败: %v\n", err)
		return
	}

	// 退款成功后减少商品销量
	if err := h.updateProductSalesOnPayment(tx, order.Id, -1); err != nil {
		tx.Rollback()
		fmt.Printf("减少商品销量失败: %v\n", err)
		return
	}

	// 可以在这里更新订单状态为已退款
	if err := tx.Model(&order).Update("status", localTypes.OrderStatusRefunded).Error; err != nil {
		tx.Rollback()
		fmt.Printf("更新订单退款状态失败: %v\n", err)
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		fmt.Printf("提交退款事务失败: %v\n", err)
		return
	}

	fmt.Printf("退款成功，订单 %s 已减少商品销量\n", refundData.OutTradeNo)
}

// updateProductSalesOnPayment 更新商品销量
// orderId: 订单ID
// increment: 1表示增加销量，-1表示减少销量
func (h *PaymentHandler) updateProductSalesOnPayment(tx *gorm.DB, orderId uint, increment int) error {
	// 查询订单详情
	var orderDetails []model.OrderDetail
	if err := tx.Where("order_id = ?", orderId).Find(&orderDetails).Error; err != nil {
		return fmt.Errorf("查询订单详情失败: %v", err)
	}

	if len(orderDetails) == 0 {
		return fmt.Errorf("订单详情为空")
	}

	// 查询订单信息，判断是否为次卡订单
	var order model.Order
	if err := tx.Where("id = ?", orderId).First(&order).Error; err != nil {
		return fmt.Errorf("查询订单信息失败: %v", err)
	}

	// 批量更新商品销量
	for _, detail := range orderDetails {
		salesChange := detail.SpuNum * increment

		// 普通商品订单，更新tb_spus表的sales_num字段
		updateSql := "UPDATE tb_spus SET sales_num = GREATEST(0, sales_num + ?) WHERE id = ?"
		if err := tx.Exec(updateSql, salesChange, detail.SpuId).Error; err != nil {
			return fmt.Errorf("更新商品 %d 销量失败: %v", detail.SpuId, err)
		}

		if increment > 0 {
			fmt.Printf("商品 %d(%s) 销量增加 %d\n", detail.SpuId, detail.SpuName, detail.SpuNum)
		} else {
			fmt.Printf("商品 %d(%s) 销量减少 %d\n", detail.SpuId, detail.SpuName, detail.SpuNum)
		}

	}

	return nil
}

// handleRefundAbnormal 处理退款异常通知
func (h *PaymentHandler) handleRefundAbnormal(req *wxchat.PayNotifyRequest, notifyData map[string]interface{}) {
	fmt.Printf("处理退款异常通知")

	// TODO: 处理退款异常的业务逻辑
	fmt.Printf("退款异常，处理业务逻辑...\n")
}

// handleRefundClosed 处理退款关闭通知
func (h *PaymentHandler) handleRefundClosed(req *wxchat.PayNotifyRequest, notifyData map[string]interface{}) {
	fmt.Printf("处理退款关闭通知")

	// TODO: 处理退款关闭的业务逻辑
	fmt.Printf("退款关闭，处理业务逻辑...\n")
}

// handleVipPaymentSuccess 处理VIP订单支付成功
func (h *PaymentHandler) handleVipPaymentSuccess(tx *gorm.DB, order *model.Order) error {
	// 1. 查询VIP等级信息
	var vipLevel model.VipLevel
	if err := tx.Where("id = ?", order.ProductId).First(&vipLevel).Error; err != nil {
		return fmt.Errorf("查询VIP等级信息失败: %v", err)
	}

	// 2. 计算VIP开始和结束时间
	now := time.Now()
	endTime := now.AddDate(0, vipLevel.DurationMonths, 0)

	// 3. 更新用户VIP记录
	if err := tx.Model(&model.UserVip{}).
		Where("order_no = ? AND status = ?", order.OrderNo, "PENDING").
		Updates(map[string]interface{}{
			"status":     "ACTIVE",
			"start_time": now,
			"end_time":   endTime,
		}).Error; err != nil {
		return fmt.Errorf("更新VIP记录失败: %v", err)
	}

	// 4. 获取该VIP等级的所有权益
	var levelBenefits []model.VipLevelBenefit
	if err := tx.Where("vip_level_id = ? AND is_active = ?", order.ProductId, true).Find(&levelBenefits).Error; err != nil {
		return fmt.Errorf("获取VIP权益失败: %v", err)
	}

	// 5. 为用户创建权益记录
	for _, levelBenefit := range levelBenefits {
		remainingCount := levelBenefit.BenefitLimit
		userVipBenefit := model.UserVipBenefit{
			UserId:         uint64(order.UserId),
			VipLevelId:     uint64(order.ProductId),
			BenefitId:      levelBenefit.BenefitId,
			BenefitValue:   levelBenefit.BenefitValue,
			BenefitLimit:   levelBenefit.BenefitLimit,
			UsedCount:      0,
			RemainingCount: remainingCount,
			StartTime:      now,
			EndTime:        &endTime,
			Status:         "ACTIVE",
			IsExpired:      false,
		}

		if err := tx.Create(&userVipBenefit).Error; err != nil {
			return fmt.Errorf("创建用户权益记录失败: %v", err)
		}
	}

	// 6. 更新或创建用户当前VIP状态
	daysRemaining := h.calculateRemainingDays(&endTime)
	userCurrentVip := model.UserCurrentVip{
		UserId:        uint64(order.UserId),
		VipLevelId:    &vipLevel.Id,
		VipLevelName:  &vipLevel.LevelName,
		VipLevelCode:  &vipLevel.LevelCode,
		VipLevelRank:  &vipLevel.LevelRank,
		StartTime:     &now,
		EndTime:       &endTime,
		DaysRemaining: daysRemaining,
		IsExpired:     false,
		UpdatedAt:     now,
	}

	// 使用Save方法，如果记录存在则更新，不存在则创建
	if err := tx.Save(&userCurrentVip).Error; err != nil {
		return fmt.Errorf("更新用户当前VIP状态失败: %v", err)
	}

	fmt.Printf("VIP订单 %s 支付成功，已创建用户VIP记录，关联权益数量: %d\n", order.OrderNo, len(levelBenefits))
	return nil
}

// writeNotifyResponse 写入回调响应
func writeNotifyResponse(w http.ResponseWriter, response *wxchat.NotifyResponseResult) {
	w.Header().Set("Content-Type", "application/json")

	if response.Code == "SUCCESS" {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusBadRequest)
	}

	jsonResponse, _ := json.Marshal(response)
	w.Write(jsonResponse)
}

// calculateRemainingDays 计算剩余天数
func (h *PaymentHandler) calculateRemainingDays(endTime *time.Time) *int {
	if endTime == nil {
		return nil
	}

	now := time.Now()
	remainingDays := int(endTime.Sub(now).Hours() / 24)
	if remainingDays < 0 {
		remainingDays = 0
	}
	return &remainingDays
}
