package tasks

import (
	"fmt"
	"haha/handler"

	"gitee.com/masculine_girl/ginbase/api/service/nsq"
	"go.uber.org/fx"
)

var BackgroundTaskOptions = []fx.Option{
	fx.Invoke(func(nsqService *nsq.NSQService, orderNsqHandler *handler.OrderNsqHandler) {
		fmt.Println("正在注册NSQ消费者: Order Timeout Handler")
		nsqService.RegisterHandler("haha_spu_order", orderNsqHandler)
		fmt.Println("NSQ消费者注册完成: haha_spu_order")
	}),
}
