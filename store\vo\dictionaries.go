package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

// Step 步骤定义
type SysDictionaryDetail struct {
	vo.GinBaseVo
	Label           string `json:"label"`             // 名称
	Value           string `json:"value"`             // 值
	Status          int    `json:"status"`            // 状态 0:禁用 1:启用
	Sort            int    `json:"sort"`              // 排序
	SysDictionaryId int    `json:"sys_dictionary_id"` // 关联标记
}
