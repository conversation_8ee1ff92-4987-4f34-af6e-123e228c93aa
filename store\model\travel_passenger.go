package model

import "gitee.com/masculine_girl/ginbase/api/store/model"

// TravelPassenger 出行人信息
type TravelPassenger struct {
	model.GinBaseModel
	OrderId       uint   `json:"order_id"`       // 关联订单ID
	Name          string `json:"name"`           // 姓名
	IdCard        string `json:"id_card"`        // 身份证号
	Phone         string `json:"phone"`          // 手机号
	PassengerType string `json:"passenger_type"` // adult(成年人) / child(小孩)
}