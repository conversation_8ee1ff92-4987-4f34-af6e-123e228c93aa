package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

// OrderStepProgress 订单步骤进度VO
type OrderStepProgress struct {
	vo.GinBaseVo
	OrderId          uint   `json:"order_id"`           // 订单ID
	SpuId            uint   `json:"spu_id"`             // 商品/服务ID
	StepId           uint   `json:"step_id"`            // 步骤定义ID
	StepName         string `json:"step_name"`          // 步骤名称
	StepCode         string `json:"step_code"`          // 步骤代码
	StepOrder        int    `json:"step_order"`         // 步骤顺序
	StepStatus       int    `json:"step_status"`        // 步骤状态: 0=未开始, 1=进行中, 2=已完成
	StartTime        int64  `json:"start_time"`         // 步骤开始时间
	EstimatedEndTime int64  `json:"estimated_end_time"` // 预计完成时间
	ActualEndTime    int64  `json:"actual_end_time"`    // 实际完成时间
	OperatorId       uint   `json:"operator_id"`        // 操作员ID
	OperatorName     string `json:"operator_name"`      // 操作员姓名
	Notes            string `json:"notes"`              // 步骤备注
	EstimatedTime    int    `json:"estimated_time"`     // 预计耗时(分钟)

	// 施工视频相关
	ConstructionVideo string `json:"construction_video"` // 施工视频URL
	VideoDuration     int    `json:"video_duration"`     // 视频时长(秒)
	VideoSize         int64  `json:"video_size"`         // 视频文件大小(字节)
	VideoUploadTime   int64  `json:"video_upload_time"`  // 视频上传时间
}
