package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type Address struct {
	vo.GinBaseVo
	UserId    int    `json:"user_id"`    // 用户ID
	UserName  string `json:"user_name"`  // 收货人姓名
	UserPhone string `json:"user_phone"` // 收货人手机号
	Province  string `json:"province"`   // 省份
	City      string `json:"city"`       // 城市
	District  string `json:"district"`   // 区/县
	Addr      string `json:"addr"`       // 详细地址
	IsDefault bool   `json:"is_default"` // 是否为默认地址
}

type AddressRequest struct {
	UserName  string `json:"user_name" binding:"required"`  // 收货人姓名
	UserPhone string `json:"user_phone" binding:"required"` // 收货人手机号
	Province  string `json:"province" binding:"required"`   // 省份
	City      string `json:"city" binding:"required"`       // 城市
	District  string `json:"district" binding:"required"`   // 区/县
	Addr      string `json:"addr"`                          // 详细地址
}
