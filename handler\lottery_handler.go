package handler

import (
	"fmt"
	"haha/store/model"
	"haha/store/vo"
	"time"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type LotteryHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

func NewLotteryHandler(app *core.AppServer, db *gorm.DB) *LotteryHandler {
	h := LotteryHandler{
		db: db,
	}
	h.App = app
	return &h
}

// List 抽奖列表接口
func (h *LotteryHandler) List(c *gin.Context) {
	// 获取查询参数
	filter := c.DefaultQuery("filter", "all")
	page := h.GetInt(c, "page", 1)
	pageSize := h.GetInt(c, "page_size", 20)

	// 构建查询
	query := h.buildLotteryListQuery(filter)

	// 计算总数
	var total int64
	countQuery := h.buildLotteryListQuery(filter)
	countQuery.Count(&total)

	// 分页查询
	var items []model.LotteryActivityPrize
	offset := (page - 1) * pageSize
	err := query.Preload("Activity").Preload("Prize").
		Offset(offset).Limit(pageSize).
		Order("activity.start_time DESC, tb_lottery_activity_prizes.id DESC").
		Find(&items).Error

	if err != nil {
		resp.ERROR(c, fmt.Sprintf("查询失败: %v", err))
		return
	}

	// 转换为VO
	var list []vo.LotteryListItemVO
	for _, item := range items {
		var lotteryVo vo.LotteryListItemVO
		err := utils.CopyObject(item, &lotteryVo)
		if err == nil && item.Activity != nil && item.Prize != nil {
			// 设置基础字段
			lotteryVo.Id = item.Id
			lotteryVo.CreatedAt = item.CreatedAt.Unix()
			lotteryVo.UpdatedAt = item.UpdatedAt.Unix()

			// 设置奖品信息
			lotteryVo.PrizeName = item.Prize.PrizeName
			lotteryVo.PrizeImage = item.Prize.PrizeImage
			lotteryVo.PrizeType = item.Prize.PrizeType
			lotteryVo.Description = item.Prize.Description

			// 设置数量信息
			lotteryVo.TotalQuantity = item.TotalQuantity
			lotteryVo.RemainingQuantity = item.RemainingQuantity

			// 设置活动信息
			lotteryVo.ActivityId = item.ActivityId
			lotteryVo.ActivityName = item.Activity.ActivityName
			lotteryVo.StartTime = item.Activity.StartTime
			lotteryVo.EndTime = item.Activity.EndTime
			lotteryVo.Status = item.Activity.Status
			lotteryVo.PointsCost = item.Activity.PointsCost
			lotteryVo.IsRecommended = item.Activity.IsRecommended

			list = append(list, lotteryVo)
		}
	}

	resp.SUCCESS(c, baseVo.NewPage(total, page, pageSize, list))
}

// buildLotteryListQuery 构建抽奖列表查询条件
func (h *LotteryHandler) buildLotteryListQuery(filter string) *gorm.DB {
	now := time.Now()

	query := h.db.Model(&model.LotteryActivityPrize{}).
		Joins("INNER JOIN tb_lottery_activities activity ON tb_lottery_activity_prizes.activity_id = activity.id").
		Joins("INNER JOIN tb_lottery_prizes prize ON tb_lottery_activity_prizes.prize_id = prize.id").
		Where("activity.deleted_at IS NULL").
		Where("prize.deleted_at IS NULL").
		Where("tb_lottery_activity_prizes.deleted_at IS NULL")

	switch filter {
	case "recommended":
		// 推荐的抽奖
		query = query.Where("activity.is_recommended = ?", true)
	case "upcoming":
		// 即将开始 (status=0 且 start_time > now)
		query = query.Where("activity.status = ? AND activity.start_time > ?", 0, now)
	case "ongoing":
		// 已开始 (status=1 且 start_time <= now <= end_time)
		query = query.Where("activity.status = ? AND activity.start_time <= ? AND activity.end_time >= ?", 1, now, now)
	case "all":
		// 所有有效的抽奖 (排除已结束的)
		query = query.Where("activity.status != ?", 2)
	}

	return query
}
