package handler

import (
	"bytes"
	"fmt"
	"haha/store/model"
	"haha/store/vo"
	util "haha/utils"
	"strings"
	"time"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/service/oss"
	"gitee.com/masculine_girl/ginbase/api/service/wxchat"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/pkg/jwt"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type MemberHandler struct {
	pkg.BaseHandler
	db              *gorm.DB
	redis           *redis.Client
	wxClient        *wxchat.WxMiNipClient
	uploaderManager *oss.UploaderManager
}

func NewMemberHandler(
	app *core.AppServer,
	db *gorm.DB,
	client *redis.Client,
	wxClient *wxchat.WxMiNipClient,
	uploaderManager *oss.UploaderManager,
) *MemberHandler {
	handler := &MemberHandler{db: db, redis: client, wxClient: wxClient, uploaderManager: uploaderManager}
	handler.App = app
	return handler
}

func (h *MemberHandler) Register(c *gin.Context) {
	// parameters process
	var data struct {
		Mobile    string `json:"mobile"`
		Password  string `json:"password"`
		InviterId uint   `json:"inviter_id"` //邀请人ID
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}
	data.Password = strings.TrimSpace(data.Password)

	if len(data.Mobile) < 10 {
		resp.ERROR(c, "请输入合法的手机号")
		return
	}
	if len(data.Password) < 8 {
		resp.ERROR(c, "密码长度不能少于8个字符")
		return
	}

	// 检查验证码
	//key := CodeStorePrefix + data.Mobile
	//if h.App.SysConfig.EnabledMsg {
	//	code, err := h.redis.Get(c, key).Result()
	//	if err != nil || code != data.Code {
	//		resp.ERROR(c, "短信验证码错误")
	//		return
	//	}
	//}

	// 验证邀请码
	//inviteCode := model.InviteCode{}
	//if data.InviteCode == "" {
	//	if h.App.SysConfig.ForceInvite {
	//		resp.ERROR(c, "当前系统设定必须使用邀请码才能注册")
	//		return
	//	}
	//} else {
	//	res := h.db.Where("code = ?", data.InviteCode).First(&inviteCode)
	//	if res.Error != nil {
	//		resp.ERROR(c, "无效的邀请码")
	//		return
	//	}
	//}

	// check if the username is exists
	var item model.Member
	res := h.db.Where("mobile = ?", data.Mobile).First(&item)
	if res.RowsAffected > 0 {
		resp.ERROR(c, "该手机号码已经被注册，请更换其他手机号")
		return
	}

	salt := utils.RandString(8)
	member := model.Member{
		Password:   utils.GenPassword(data.Password, salt),
		Avatar:     "/images/avatar/user.png",
		Salt:       salt,
		Status:     true,
		Mobile:     data.Mobile,
		AgentCalls: 1,
	}
	res = h.db.Create(&member)
	if res.Error != nil {
		resp.ERROR(c, "保存数据失败")
		return
	}
	// 记录邀请关系
	//if data.InviterId > 0 {
	//	// 增加邀请数量
	//	//h.db.Model(&model.Member{}).Where("id = ?", data.InviterId).UpdateColumn("reg_num", gorm.Expr("reg_num + ?", 1))
	//
	//	// 添加邀请记录
	//	h.db.Create(&model.InviteLog{
	//		InviterId: data.InviterId,
	//		UserId:    member.Id,
	//		Username:  member.Mobile,
	//	})
	//}

	// 自动登录创建 token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": member.Id,
		"expired": time.Now().Add(time.Second * time.Duration(h.App.Config.Session.MaxAge)).Unix(),
	})
	tokenString, err := token.SignedString([]byte(h.App.Config.Session.SecretKey))
	if err != nil {
		resp.ERROR(c, "Failed to generate token, "+err.Error())
		return
	}
	// 保存到 redis
	key := fmt.Sprintf("members/%d", member.Id)
	if _, err := h.redis.Set(c, key, tokenString, 0).Result(); err != nil {
		resp.ERROR(c, "error with save token: "+err.Error())
		return
	}
	// 生成refresh_token
	refreshToken := utils.RandString(32)
	refreshKey := fmt.Sprintf("refresh_members/%d", member.Id)
	// 设置refresh_token 30天过期时间
	refreshExpiration := time.Hour * 24 * 30 // 30天
	if _, err := h.redis.Set(c, refreshKey, refreshToken, refreshExpiration).Result(); err != nil {
		resp.ERROR(c, "error with save refresh token: "+err.Error())
		return
	}
	resp.SUCCESS(c, tokenString)
}

// Login 用户登录
func (h *MemberHandler) Login(c *gin.Context) {
	var data struct {
		Mobile   string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}
	var member model.Member
	res := h.db.Where("mobile = ?", data.Mobile).First(&member)
	if res.Error != nil {
		resp.ERROR(c, "用户名不存在")
		return
	}

	password := utils.GenPassword(data.Password, member.Salt)
	if password != member.Password {
		resp.ERROR(c, "用户名或密码错误")
		return
	}

	if !member.Status {
		resp.ERROR(c, "该用户已被禁止登录，请联系管理员")
		return
	}

	// 更新最后登录时间和IP
	member.LastLoginIp = c.ClientIP()
	member.LastLoginAt = time.Now().Unix()
	h.db.Model(&member).Updates(member)

	//h.db.Create(&model.UserLoginLog{
	//	UserId:       user.Id,
	//	Username:     user.Mobile,
	//	LoginIp:      c.ClientIP(),
	//	LoginAddress: utils.Ip2Region(h.searcher, c.ClientIP()),
	//})

	// 创建 token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": member.Id,
		"expired": time.Now().Add(time.Second * time.Duration(h.App.Config.Session.MaxAge)).Unix(),
	})
	tokenString, err := token.SignedString([]byte(h.App.Config.Session.SecretKey))
	if err != nil {
		resp.ERROR(c, "Failed to generate token, "+err.Error())
		return
	}
	// 保存到 redis
	key := fmt.Sprintf("members/%d", member.Id)
	if _, err := h.redis.Set(c, key, tokenString, 0).Result(); err != nil {
		resp.ERROR(c, "error with save token: "+err.Error())
		return
	}
	resp.SUCCESS(c, tokenString)
}

// WxLogin 用户微信小程序登录
func (h *MemberHandler) WxLogin(c *gin.Context) {
	var data struct {
		Code        string `json:"code" binding:"required"`
		PhoneCode   string `json:"phone_code"`
		NickName    string `json:"nick_name"`
		Avatar      string `json:"avatar"`
		Mobile      string `json:"mobile"`
		InviterCode string `json:"inviter_code"` // 邀请码
	}

	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	// 支持从多个来源获取邀请码
	inviterCode := data.InviterCode
	if inviterCode == "" {
		// 从query参数获取
		inviterCode = c.Query("inviter_code")
	}
	if inviterCode == "" {
		// 从scene参数获取（小程序扫码）
		inviterCode = c.Query("scene")
	}

	authSession := h.wxClient.GetSessionKey(c, data.Code)
	if authSession == nil {
		resp.ERROR(c, "code失效")
		return
	}

	if data.PhoneCode != "" {
		phone, err := h.wxClient.GetPhone(data.PhoneCode)
		if err == nil {
			data.Mobile = phone
		}
	}

	if data.NickName == "" {
		data.NickName = fmt.Sprintf("哈哈%s", utils.GetRandomName())
	}

	var member model.Member
	res := h.db.Where("open_id = ?", authSession.OpenID).First(&member)
	isNewUser := false
	if res.Error != nil || res.RowsAffected == 0 {
		//没有用户，新增用户
		salt := utils.RandString(8)
		member = model.Member{
			Password:   utils.GenPassword(salt, salt),
			Nick:       data.NickName,
			Avatar:     "",
			Salt:       salt,
			Status:     true,
			Mobile:     data.Mobile,
			OpenId:     authSession.OpenID,
			AgentCalls: 1,
		}
		res = h.db.Create(&member)
		if res.Error != nil {
			resp.ERROR(c, "保存数据失败")
			return
		}
		isNewUser = true
	} else if !member.Status {
		resp.ERROR(c, "该用户已被禁止登录，请联系管理员")
		return
	}

	// 老用户手机号补写（仅当通过 phone_code 获取到手机号，且当前账号手机号为空时）
	if !isNewUser && data.PhoneCode != "" && data.Mobile != "" && member.Mobile == "" {
		// 检查该手机号是否已经被其他账号绑定
		var cnt int64
		if err := h.db.Model(&model.Member{}).
			Where("mobile = ? AND id <> ?", data.Mobile, member.Id).
			Count(&cnt).Error; err != nil {
			resp.ERROR(c, "服务异常，请稍后再试")
			return
		}
		if cnt > 0 {
			resp.ERROR(c, "该手机号已被其他账号绑定")
			return
		}
		// 并发安全：仅当当前mobile为空时更新
		if err := h.db.Model(&model.Member{}).
			Where("id = ? AND (mobile = '' OR mobile IS NULL)", member.Id).
			Update("mobile", data.Mobile).Error; err != nil {
			resp.ERROR(c, "绑定手机号失败")
			return
		}
		member.Mobile = data.Mobile
	}

	// 如果是新用户且有邀请码，处理邀请关系
	if isNewUser && inviterCode != "" {
		if err := h.bindInviterIfNeeded(member.Id, inviterCode); err != nil {
			// 邀请绑定失败不影响登录，只是记录日志或返回警告
			// 可以在这里记录日志或返回提示信息
		}
	}

	// 更新最后登录时间和IP
	member.LastLoginIp = c.ClientIP()
	member.LastLoginAt = time.Now().Unix()
	h.db.Model(&member).Updates(member)

	// 重新从数据库获取用户信息，确保ParentId是最新的
	h.db.First(&member, member.Id)

	// 创建 token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": member.Id,
		"expired": time.Now().Add(time.Second * time.Duration(h.App.Config.Session.MaxAge)).Unix(),
	})
	tokenString, err := token.SignedString([]byte(h.App.Config.Session.SecretKey))
	if err != nil {
		resp.ERROR(c, "Failed to generate token, "+err.Error())
		return
	}
	// 保存到 redis
	key := fmt.Sprintf("members/%d", member.Id)
	if _, err := h.redis.Set(c, key, tokenString, 0).Result(); err != nil {
		resp.ERROR(c, "error with save token: "+err.Error())
		return
	}
	// 生成refresh_token
	refreshToken := utils.RandString(32)
	refreshKey := fmt.Sprintf("refresh_members/%d", member.Id)
	// 设置refresh_token 30天过期时间
	refreshExpiration := time.Hour * 24 * 30 // 30天
	if _, err := h.redis.Set(c, refreshKey, refreshToken, refreshExpiration).Result(); err != nil {
		resp.ERROR(c, "error with save refresh token: "+err.Error())
		return
	}

	// 判断是否需要前端弹出邀请码输入框
	needInviteCode := false
	if isNewUser && member.ParentId == 0 {
		// 新用户且没有上级，提示输入邀请码
		needInviteCode = true
	}

	// 返回登录信息
	response := map[string]interface{}{
		"token":            tokenString,
		"refresh_token":    refreshToken,
		"need_invite_code": needInviteCode,
	}
	resp.SUCCESS(c, response)
}

type userProfile struct {
	Id     uint   `json:"id"`
	Nick   string `json:"nick"`
	Mobile string `json:"mobile"`
	Avatar string `json:"avatar"`
	Yue    int    `json:"yue"`
	OpenId string `json:"open_id"`
	Types  int    `json:"types"` //0=用户 1=工作人员
}

func (h *MemberHandler) Profile(c *gin.Context) {
	user, err := util.GetLoginUser(c, h.db)
	if err != nil {
		resp.NotAuth(c)
		return
	}

	h.db.First(&user, user.Id)
	var profile userProfile
	err = utils.CopyObject(user, &profile)
	if err != nil {
		resp.ERROR(c, "获取用户信息失败")
		return
	}

	profile.Id = user.Id
	resp.SUCCESS(c, profile)
}

func (h *MemberHandler) ProfileUpdate(c *gin.Context) {
	var data userProfile
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	user, err := util.GetLoginUser(c, h.db)
	if err != nil {
		resp.NotAuth(c)
		return
	}
	h.db.First(&user, user.Id)
	user.Nick = data.Nick
	user.Avatar = data.Avatar
	res := h.db.Updates(&user)
	if res.Error != nil {
		resp.ERROR(c, "更新用户信息失败")
		return
	}

	resp.SUCCESS(c)
}

// UpdatePass 更新密码
func (h *MemberHandler) UpdatePass(c *gin.Context) {
	var data struct {
		OldPass  string `json:"old_pass"`
		Password string `json:"password"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	if len(data.Password) < 8 {
		resp.ERROR(c, "密码长度不能少于8个字符")
		return
	}

	user, err := util.GetLoginUser(c, h.db)
	if err != nil {
		resp.NotAuth(c)
		return
	}

	password := utils.GenPassword(data.OldPass, user.Salt)
	if password != user.Password {
		resp.ERROR(c, "原密码错误")
		return
	}

	newPass := utils.GenPassword(data.Password, user.Salt)
	res := h.db.Model(&user).UpdateColumn("password", newPass)
	if res.Error != nil {
		resp.ERROR(c, "更新数据库失败")
		return
	}

	resp.SUCCESS(c)
}

func (h *MemberHandler) WxSilentLogin(c *gin.Context) {
	var data struct {
		Code        string `json:"code" binding:"required"`
		InviterCode string `json:"inviter_code"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	// 支持从多个来源获取邀请码
	inviterCode := strings.TrimSpace(data.InviterCode)
	if inviterCode == "" {
		inviterCode = c.Query("inviter_code")
	}
	if inviterCode == "" {
		inviterCode = c.Query("scene")
	}

	authSession := h.wxClient.GetSessionKey(c, data.Code)
	if authSession == nil {
		resp.ERROR(c, "code失效")
		return
	}

	var member model.Member
	res := h.db.Where("open_id = ?", authSession.OpenID).First(&member)
	isNewUser := false
	if res.Error != nil || res.RowsAffected == 0 {
		// 创建新用户（静默，不要求手机号）
		salt := utils.RandString(8)
		member = model.Member{
			Password:   utils.GenPassword(salt, salt),
			Nick:       fmt.Sprintf("哈哈%s", utils.GetRandomName()),
			Avatar:     "",
			Salt:       salt,
			Status:     true,
			Mobile:     "",
			OpenId:     authSession.OpenID,
			AgentCalls: 1,
		}
		if err := h.db.Create(&member).Error; err != nil {
			resp.ERROR(c, "保存数据失败")
			return
		}
		isNewUser = true
	} else if !member.Status {
		resp.ERROR(c, "该用户已被禁止登录，请联系管理员")
		return
	}

	// 新用户带邀请码则自动绑定上级（老用户不自动绑定，交给前端显式确认）
	if isNewUser && inviterCode != "" {
		_ = h.bindInviterIfNeeded(member.Id, inviterCode)
	}

	// 更新最后登录时间和IP
	member.LastLoginIp = c.ClientIP()
	member.LastLoginAt = time.Now().Unix()
	h.db.Model(&member).Updates(member)

	// 重新获取，确保最新parent_id
	h.db.Select("id", "parent_id").First(&member, member.Id)

	// 生成 token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": member.Id,
		"expired": time.Now().Add(time.Second * time.Duration(h.App.Config.Session.MaxAge)).Unix(),
	})
	tokenString, err := token.SignedString([]byte(h.App.Config.Session.SecretKey))
	if err != nil {
		resp.ERROR(c, "Failed to generate token, "+err.Error())
		return
	}
	// 保存到 redis
	key := fmt.Sprintf("members/%d", member.Id)
	if _, err := h.redis.Set(c, key, tokenString, 0).Result(); err != nil {
		resp.ERROR(c, "error with save token: "+err.Error())
		return
	}
	// 生成refresh_token（与 WxLogin 保持一致）
	refreshToken := utils.RandString(32)
	refreshKey := fmt.Sprintf("refresh_members/%d", member.Id)
	refreshExpiration := time.Hour * 24 * 30 // 30天
	if _, err := h.redis.Set(c, refreshKey, refreshToken, refreshExpiration).Result(); err != nil {
		resp.ERROR(c, "error with save refresh token: "+err.Error())
		return
	}

	// 计算引导标志
	needPhoneAuth := member.Mobile == ""
	needInviteCode := false
	if isNewUser && member.ParentId == 0 {
		needInviteCode = true
	}
	if !isNewUser && member.ParentId == 0 && inviterCode != "" {
		// 老用户扫码带邀请码，但尚未绑定上级，建议前端显式提示是否绑定
		needInviteCode = true
	}

	// 准备返回的用户信息
	userInfo := map[string]interface{}{
		"id":     member.Id,
		"nick":   member.Nick,
		"mobile": member.Mobile,
		"avatar": member.Avatar,
	}

	// 已绑定上级则返回上级概要信息（用于“由XXX邀请”提示）
	var inviterInfo map[string]interface{}
	if member.ParentId > 0 {
		var inviter model.Member
		if err := h.db.Select("id", "nick", "invite_code").Where("id = ?", member.ParentId).First(&inviter).Error; err == nil {
			inviterInfo = map[string]interface{}{
				"id":          inviter.Id,
				"nick":        inviter.Nick,
				"invite_code": inviter.InviteCode,
			}
		}
	}

	resp.SUCCESS(c, map[string]interface{}{
		"is_new_user":      isNewUser,
		"need_phone_auth":  needPhoneAuth,
		"need_invite_code": needInviteCode,
		"token":            tokenString,
		"refresh_token":    refreshToken,
		"user_info":        userInfo,
		"inviter_info":     inviterInfo,
	})
}

// ... existing code ...
// GetInviteStats 获取当前用户的邀请统计信息
func (h *MemberHandler) GetInviteStats(c *gin.Context) {
	// 获取当前登录用户
	user, err := util.GetLoginUser(c, h.db)
	if err != nil {
		resp.NotAuth(c)
		return
	}

	// 统计累计邀请人数
	var totalInvited int64
	h.db.Model(&model.InviteRecord{}).
		Where("inviter_id = ? AND invitee_id IS NOT NULL", user.Id).
		Count(&totalInvited)

	// 统计今日邀请人数
	var todayInvited int64
	todayStart := time.Now().Truncate(24 * time.Hour)
	h.db.Model(&model.InviteRecord{}).
		Where("inviter_id = ? AND invitee_id IS NOT NULL AND created_at >= ?", user.Id, todayStart).
		Count(&todayInvited)

	// 统计累计奖励积分
	var totalRewards int64
	h.db.Model(&model.InviteRecord{}).
		Where("inviter_id = ? AND reward_status = 1", user.Id). // 只统计已发放的奖励
		Select("COALESCE(SUM(reward_points), 0)").
		Scan(&totalRewards)

	// 构造返回结果
	stats := vo.InviteStats{
		TotalInvited: int(totalInvited),
		TodayInvited: int(todayInvited),
		TotalRewards: int(totalRewards),
	}

	resp.SUCCESS(c, stats)
}

// BindInviter 允许已登录用户通过邀请码补绑上级
func (h *MemberHandler) BindInviter(c *gin.Context) {
	var data struct {
		InviterCode string `json:"inviter_code" binding:"required"`
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	user, err := util.GetLoginUser(c, h.db)
	if err != nil {
		resp.NotAuth(c)
		return
	}

	if err := h.bindInviterIfNeeded(user.Id, data.InviterCode); err != nil {
		resp.ERROR(c, err.Error())
		return
	}

	resp.SUCCESS(c)
}

// bindInviterIfNeeded 根据邀请码绑定上级，满足以下约束：
// 1) 一个用户只能有一个上级（已有上级则不能再绑定）
// 2) 禁止自我邀请
// 3) 禁止成环（检测上级祖先链中是否存在自己）
func (h *MemberHandler) bindInviterIfNeeded(userID uint, inviterCode string) error {
	inviterCode = strings.TrimSpace(inviterCode)
	if inviterCode == "" {
		return fmt.Errorf("邀请码不能为空")
	}

	// 查询当前用户
	var user model.Member
	if err := h.db.Select("id", "parent_id").Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("用户不存在")
	}
	if user.ParentId > 0 {
		return fmt.Errorf("已绑定上级，无法重复绑定")
	}

	// 查询邀请人
	var inviter model.Member
	if err := h.db.Where("invite_code = ?", inviterCode).First(&inviter).Error; err != nil {
		return fmt.Errorf("无效的邀请码")
	}
	if inviter.Id == userID {
		return fmt.Errorf("不能绑定自己为上级")
	}

	// 检测环：从inviter开始沿着parent_id向上遍历，若遇到userID则成环
	cur := inviter
	for depth := 0; depth < 20 && cur.ParentId > 0; depth++ { // 防御性限制深度
		var p model.Member
		if err := h.db.Select("id", "parent_id").Where("id = ?", cur.ParentId).First(&p).Error; err != nil {
			break // 祖先不存在则视为无环
		}
		if p.Id == userID {
			return fmt.Errorf("邀请关系非法：存在循环邀请")
		}
		cur = p
	}

	// 并发安全：仅当当前parent_id=0时更新
	res := h.db.Model(&model.Member{}).
		Where("id = ? AND (parent_id = 0 OR parent_id IS NULL)", userID).
		Update("parent_id", inviter.Id)
	if res.Error != nil {
		return fmt.Errorf("绑定失败，请稍后重试")
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("已绑定上级或绑定被并发抢占")
	}

	// 插入邀请记录
	campaign := "推广活动"
	channel := "qr"
	now := time.Now()
	inviteRecord := model.InviteRecord{
		CreatedAt:    now,
		UpdatedAt:    now,
		InviterId:    inviter.Id,
		InviteeId:    &userID, // 绑定后回填被邀请人ID
		RewardPoints: 0,       // 默认值，可根据业务需求修改
		Campaign:     &campaign,
		Channel:      &channel,
	}
	if err := h.db.Create(&inviteRecord).Error; err != nil {
		// 记录日志，但不中断主流程
		fmt.Printf("创建邀请记录失败: %v\n", err)
	}

	return nil
}

// ResetPass 重置密码
func (h *MemberHandler) ResetPass(c *gin.Context) {
	var data struct {
		Mobile   string
		Code     string // 验证码
		Password string // 新密码
	}
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	var user model.Member
	res := h.db.Where("mobile", data.Mobile).First(&user)
	if res.Error != nil {
		resp.ERROR(c, "用户不存在！")
		return
	}

	// 检查验证码
	//key := CodeStorePrefix + data.Mobile
	//if h.App.SysConfig.EnabledMsg {
	//	code, err := h.redis.Get(c, key).Result()
	//	if err != nil || code != data.Code {
	//		resp.ERROR(c, "短信验证码错误")
	//		return
	//	}
	//}

	password := utils.GenPassword(data.Password, user.Salt)
	user.Password = password
	res = h.db.Updates(&user)
	if res.Error != nil {
		resp.ERROR(c)
	} else {
		//h.redis.Del(c, key)
		resp.SUCCESS(c)
	}
}

// BindMobile 绑定手机号
//func (h *MemberHandler) BindMobile(c *gin.Context) {
//	var data struct {
//		Mobile string `json:"mobile"`
//		Code   string `json:"code"`
//	}
//	if err := c.ShouldBindJSON(&data); err != nil {
//		resp.ERROR(c, types.InvalidArgs)
//		return
//	}
//
//	// 检查验证码
//	key := CodeStorePrefix + data.Mobile
//	code, err := h.redis.Get(c, key).Result()
//	if err != nil || code != data.Code {
//		resp.ERROR(c, "短信验证码错误")
//		return
//	}
//
//	// 检查手机号是否被其他账号绑定
//	var item model.User
//	res := h.db.Where("mobile = ?", data.Mobile).First(&item)
//	if res.Error == nil {
//		resp.ERROR(c, "该手机号已经被其他账号绑定")
//		return
//	}
//
//	user, err := utils.GetLoginUser(c, h.db)
//	if err != nil {
//		resp.NotAuth(c)
//		return
//	}
//
//	res = h.db.Model(&user).UpdateColumn("mobile", data.Mobile)
//	if res.Error != nil {
//		resp.ERROR(c, "更新数据库失败")
//		return
//	}
//
//	_ = h.redis.Del(c, key) // 删除短信验证码
//	resp.SUCCESS(c)
//}

// VipStatus 获取用户VIP状态
func (h *MemberHandler) VipStatus(c *gin.Context) {
	// 获取当前登录用户
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint64(userIDAny.(float64))

	// 查询用户VIP状态
	var vipInfo model.UserCurrentVip
	res := h.db.Where("user_id = ?", userID).First(&vipInfo)

	// 创建返回的VO对象
	var vipVO vo.UserCurrentVip

	if res.Error != nil {
		if res.Error == gorm.ErrRecordNotFound {
			// 用户没有VIP记录，返回非VIP状态
			vipVO = vo.UserCurrentVip{
				UserId:        userID,
				IsVip:         false,
				VipStatus:     "none",
				VipStatusText: "非VIP用户",
				UpdatedAt:     time.Now().Unix(),
			}
		} else {
			resp.ERROR(c, "查询VIP状态失败")
			return
		}
	} else {
		// 有VIP记录，转换为VO对象
		err := utils.CopyObject(vipInfo, &vipVO)
		if err != nil {
			resp.ERROR(c, "数据转换失败")
			return
		}

		// 设置时间戳
		vipVO.UpdatedAt = vipInfo.UpdatedAt.Unix()
		if vipInfo.StartTime != nil {
			startTime := vipInfo.StartTime.Unix()
			vipVO.StartTime = &startTime
		}
		if vipInfo.EndTime != nil {
			endTime := vipInfo.EndTime.Unix()
			vipVO.EndTime = &endTime
		}

		// 判断VIP状态
		now := time.Now()
		vipVO.IsVip = vipInfo.VipLevelId != nil && *vipInfo.VipLevelId > 0

		if !vipVO.IsVip {
			vipVO.VipStatus = "none"
			vipVO.VipStatusText = "非VIP用户"
		} else if vipInfo.IsExpired || (vipInfo.EndTime != nil && vipInfo.EndTime.Before(now)) {
			vipVO.VipStatus = "expired"
			vipVO.VipStatusText = "VIP已过期"
			vipVO.IsVip = false
		} else {
			vipVO.VipStatus = "active"
			vipVO.VipStatusText = "VIP有效"
		}

		// 计算剩余天数（实时计算，确保准确性）
		if vipVO.IsVip && vipInfo.EndTime != nil {
			remainingDays := int(vipInfo.EndTime.Sub(now).Hours() / 24)
			if remainingDays < 0 {
				remainingDays = 0
			}
			vipVO.DaysRemaining = &remainingDays
		}
	}

	resp.SUCCESS(c, vipVO)
}

// VipStatusSummary 获取用户VIP状态概要（简化版本）
func (h *MemberHandler) VipStatusSummary(c *gin.Context) {
	// 获取当前登录用户
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint64(userIDAny.(float64))

	// 查询用户VIP状态
	var vipInfo model.UserCurrentVip
	res := h.db.Where("user_id = ?", userID).First(&vipInfo)

	// 创建概要信息
	summary := vo.VipStatusSummary{
		IsVip:         false,
		VipLevelName:  "非VIP用户",
		VipStatus:     "none",
		DaysRemaining: 0,
	}

	if res.Error == nil && vipInfo.VipLevelId != nil && *vipInfo.VipLevelId > 0 {
		now := time.Now()

		// 检查是否过期
		if !vipInfo.IsExpired && (vipInfo.EndTime == nil || vipInfo.EndTime.After(now)) {
			summary.IsVip = true
			summary.VipStatus = "active"

			if vipInfo.VipLevelName != nil {
				summary.VipLevelName = *vipInfo.VipLevelName
			} else {
				summary.VipLevelName = "VIP用户"
			}

			// 计算剩余天数
			if vipInfo.EndTime != nil {
				remainingDays := int(vipInfo.EndTime.Sub(now).Hours() / 24)
				if remainingDays < 0 {
					remainingDays = 0
				}
				summary.DaysRemaining = remainingDays
			}
		} else {
			summary.VipStatus = "expired"
			summary.VipLevelName = "VIP已过期"
		}
	}

	resp.SUCCESS(c, summary)
}

// RefreshToken 刷新token接口
func (h *MemberHandler) RefreshToken(c *gin.Context) {
	// 1. 从Authorization头获取过期的access token
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		resp.ERROR(c, "缺少Authorization头")
		return
	}

	// 提取token，去掉"Bearer "前缀
	tokenString := authHeader
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		tokenString = authHeader[7:]
	}

	// 2. 使用jwt.decode()解析（即使过期也能解析）解析出用户id
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(h.App.Config.Session.SecretKey), nil
	})

	if err != nil {
		// 检查是否是过期错误，如果是过期错误，我们仍然可以解析claims
		if validationError, ok := err.(*jwt.ValidationError); ok {
			if validationError.Errors&jwt.ValidationErrorExpired != 0 {
				// token过期，但我们仍然可以解析claims
				if token != nil && token.Claims != nil {
					// 继续处理
				} else {
					resp.ERROR(c, "token格式错误")
					return
				}
			} else {
				resp.ERROR(c, "无效的token")
				return
			}
		} else {
			resp.ERROR(c, "无效的token")
			return
		}
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		resp.ERROR(c, "token格式错误")
		return
	}

	userID, ok := claims["user_id"].(float64)
	if !ok {
		resp.ERROR(c, "token中缺少用户ID")
		return
	}

	// 3. 从Redis查找对应的refresh token
	refreshKey := fmt.Sprintf("refresh_members/%d", uint(userID))
	storedRefreshToken, err := h.redis.Get(c, refreshKey).Result()
	if err != nil {
		resp.ERROR(c, "refresh token不存在或已过期")
		return
	}

	// 4. 验证refresh token存在（不需要前端传入，直接验证Redis中是否存在）
	if storedRefreshToken == "" {
		resp.ERROR(c, "refresh token无效")
		return
	}

	// 5. 生成新token（复用微信登录的token生成方法）
	newToken := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id": uint(userID),
		"expired": time.Now().Add(time.Second * time.Duration(h.App.Config.Session.MaxAge)).Unix(),
	})
	newTokenString, err := newToken.SignedString([]byte(h.App.Config.Session.SecretKey))
	if err != nil {
		resp.ERROR(c, "Failed to generate token, "+err.Error())
		return
	}

	// 6. 生成新的refresh_token
	newRefreshToken := utils.RandString(32)

	// 7. 新的token和新的refresh_token存入redis
	key := fmt.Sprintf("members/%d", uint(userID))
	if _, err := h.redis.Set(c, key, newTokenString, 0).Result(); err != nil {
		resp.ERROR(c, "error with save token: "+err.Error())
		return
	}

	// 设置新的refresh_token 30天过期时间
	refreshExpiration := time.Hour * 24 * 30 // 30天
	if _, err := h.redis.Set(c, refreshKey, newRefreshToken, refreshExpiration).Result(); err != nil {
		resp.ERROR(c, "error with save refresh token: "+err.Error())
		return
	}

	// 返回新的token和refresh_token
	response := map[string]interface{}{
		"token":         newTokenString,
		"refresh_token": newRefreshToken,
	}
	resp.SUCCESS(c, response)
}

// GenerateInviteCode 生成或获取用户邀请码及二维码
func (h *MemberHandler) GenerateInviteCode(c *gin.Context) {
	// 获取登录用户
	member, err := util.GetLoginUser(c, h.db)
	if err != nil {
		resp.ERROR(c, "用户未登录")
		return
	}

	// force 参数：是否强制刷新二维码
	force := false
	forceStr := c.Query("force")
	if forceStr == "true" || forceStr == "1" { // 简单判断
		force = true
	}

	// 幂等：已有邀请码则复用，否则生成新的
	inviteCode := member.InviteCode
	if inviteCode == "" {
		// 生成唯一邀请码
		for i := 0; i < 5; i++ {
			code := strings.ToUpper(utils.RandString(8))
			var cnt int64
			if err := h.db.Model(&model.Member{}).Where("invite_code = ?", code).Count(&cnt).Error; err != nil {
				resp.ERROR(c, "生成邀请码失败")
				return
			}
			if cnt == 0 {
				inviteCode = code
				break
			}
		}
		if inviteCode == "" {
			resp.ERROR(c, "生成邀请码失败，请稍后重试")
			return
		}
		if err := h.db.Model(&model.Member{}).Where("id = ?", member.Id).Update("invite_code", inviteCode).Error; err != nil {
			resp.ERROR(c, "保存邀请码失败")
			return
		}
		member.InviteCode = inviteCode
	}

	// 若已有二维码且未强制刷新，直接返回
	if member.InviteQrcodeUrl != "" && !force {
		resp.SUCCESS(c, gin.H{
			"invite_code":       member.InviteCode,
			"invite_qrcode_url": member.InviteQrcodeUrl,
		})
		return
	}

	// 生成微信小程序码
	// 使用 scene=inviteCode，page 可根据小程序实际注册页调整
	page := "pages/register/index"
	qr := h.wxClient.GetWxaCodeUnLimit(c, inviteCode, 280, page, false, "release")
	if qr == nil {
		resp.ERROR(c, "生成小程序码失败")
		return
	}

	// 生成二维码图片并上传到 OSS
	qrcodeUrl, err := h.uploadQRCodeToOSS(c, qr, member.Id)
	if err != nil {
		resp.ERROR(c, "上传二维码失败: "+err.Error())
		return
	}

	// 保存二维码 URL
	if err := h.db.Model(&model.Member{}).Where("id = ?", member.Id).Update("invite_qrcode_url", qrcodeUrl).Error; err != nil {
		resp.ERROR(c, "保存二维码失败")
		return
	}

	resp.SUCCESS(c, gin.H{
		"invite_code":       inviteCode,
		"invite_qrcode_url": qrcodeUrl,
	})
}

// uploadQRCodeToOSS 将微信二维码上传到存储
func (h *MemberHandler) uploadQRCodeToOSS(c *gin.Context, qr interface{}, userId uint) (string, error) {
	// 生成对象名
	objectKey := fmt.Sprintf("invite/qrcode_%d.png", userId)

	// 提取二维码字节
	var data []byte
	switch v := qr.(type) {
	case interface{ GetBuffer() []byte }:
		data = v.GetBuffer()
	case *bytes.Buffer:
		data = v.Bytes()
	case []byte:
		data = v
	default:
		// 尝试反射字段 Buffer
		type hasBuf struct{ Buffer []byte }
		if hb, ok := any(v).(hasBuf); ok {
			data = hb.Buffer
		}
	}
	if len(data) == 0 {
		// 如果没有拿到字节，直接回退到构造URL（让功能先可用）
		return h.buildOSSURL(objectKey), nil
	}

	// 使用上传管理器上传字节
	if h.uploaderManager != nil {
		uploader := h.uploaderManager.GetUploadHandler()
		if uploader != nil {
			// 优先尝试具备 PutBytes 方法的实现
			if putBytes, ok := any(uploader).(interface {
				PutBytes(c *gin.Context, objectKey string, contentType string, data []byte) (string, error)
			}); ok {
				if url, err := putBytes.PutBytes(c, objectKey, "image/png", data); err == nil && url != "" {
					return url, nil
				}
			}
		}
	}

	// 回退：根据配置构造URL
	return h.buildOSSURL(objectKey), nil
}

// buildOSSURL 根据配置构造OSS完整URL
func (h *MemberHandler) buildOSSURL(objectKey string) string {
	ossConfig := h.App.Config.OSS
	if ossConfig.AliYun.Domain != "" {
		baseURL := ossConfig.AliYun.Domain
		if strings.HasSuffix(baseURL, "/") {
			baseURL = baseURL[:len(baseURL)-1]
		}
		if ossConfig.AliYun.SubDir != "" {
			return fmt.Sprintf("%s/%s/%s", baseURL, ossConfig.AliYun.SubDir, objectKey)
		}
		return fmt.Sprintf("%s/%s", baseURL, objectKey)
	}
	return fmt.Sprintf("https://oss.csdu.net/ztl/%s", objectKey)
}
