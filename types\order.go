package types

type OrderStatus int

// 订单类型常量
const (
	OrderTypeTravel = 1 // 线路订单
	OrderTypeHotel  = 2 // 酒店订单
)

// 出行人类型常量
const (
	PassengerTypeAdult = "adult" // 成年人
	PassengerTypeChild = "child" // 小孩
)

const (
	OrderNotPaid           = OrderStatus(1)  // 未支付
	OrderPaidSuccess       = OrderStatus(2)  // 支付成功,待配送
	OrderStatusDelivered   = OrderStatus(3)  // 配送中，待收货
	OrderStatusReceived    = OrderStatus(4)  // 已收货
	OrderStatusNotReviewed = OrderStatus(5)  // 待评价
	OrderStatusFinish      = OrderStatus(10) // 已完成
	OrderStatusCancel      = OrderStatus(11) // 已取消
	OrderStatusRefund      = OrderStatus(12) // 退款中
	OrderStatusRefunded    = OrderStatus(13) // 已退款
)

type OrderRemark struct {
	Name  string  `json:"name"` // 产品名称
	Price float64 `json:"price"`
	Photo string  `json:"photo"`
}

type OrderRequest struct {
	ProductId    uint      `json:"product_id" binding:"required"`
	Mobile       string    `json:"mobile" binding:"required"`
	Amount       float64   `json:"amount" binding:"required"`
	PayWay       string    `json:"pay_way"`
	OrderType    int       `json:"order_type" binding:"required"` // 订单类型：1=线路订单，2=酒店订单
	UserName     string    `json:"user_name"`
	UserPhone    string    `json:"user_phone"`
	Province     string    `json:"province"`
	City         string    `json:"city"`
	District     string    `json:"district"`
	Addr         string    `json:"addr"`
	DeliveryTime string    `json:"delivery_time"`
	Invoice      string    `json:"invoice"`
	Memo         string    `json:"memo"`
	Goods        []Product `json:"goods"`
	CartIds      []uint    `json:"cart_ids"` // 购物车ID数组，用于下单后删除购物车

	// 门店信息（普通订单和预约订单都需要）
	StoreId   *uint  `json:"store_id,omitempty"`   // 门店ID（可选）
	StoreName string `json:"store_name,omitempty"` // 门店名称（可选）

	// 服务备注（普通订单和预约订单都需要）
	ServiceNotes string `json:"service_notes,omitempty"` // 服务备注（可选）

	// 酒店订单字段
	HotelCheckinTime  string `json:"hotel_checkin_time,omitempty"`  // 入住时间
	HotelCheckoutTime string `json:"hotel_checkout_time,omitempty"` // 离店时间
	HotelRoomType     string `json:"hotel_room_type,omitempty"`     // 房型

	// 线路订单字段
	TravelPassengers []TravelPassengerRequest `json:"travel_passengers,omitempty"` // 出行人信息
}

type Product struct {
	Id  uint `json:"id"`
	Num int  `json:"num"`
}

// TravelPassengerRequest 出行人信息请求结构体
type TravelPassengerRequest struct {
	Name          string `json:"name" binding:"required"`           // 姓名
	IdCard        string `json:"id_card" binding:"required"`        // 身份证号
	Phone         string `json:"phone" binding:"required"`          // 手机号
	PassengerType string `json:"passenger_type" binding:"required"` // adult(成年人) / child(小孩)
}
