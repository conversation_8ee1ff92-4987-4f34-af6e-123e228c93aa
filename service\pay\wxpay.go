package pay

import (
	"context"
	"gitee.com/masculine_girl/ginbase/api/service/wxchat"
)

type WxPayService struct {
	WxPayClient *wxchat.WxPayClient
}

func NewWxPayService(wxPay *wxchat.WxPayClient) *WxPayService {
	handler := &WxPayService{WxPayClient: wxPay}
	return handler
}

func (h *WxPayService) JSAPIOrder(ctx context.Context, params *wxchat.JSAPIOrderRequest) (*wxchat.JSAPIOrderResponse, error) {
	response, err := h.WxPayClient.CreateJSAPIOrder(ctx, params)
	if err != nil {
		return nil, err
	}

	return response, nil
}
