package vo

import (
	localModel "haha/store/model"
	"haha/types"

	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type Order struct {
	vo.GinBaseVo
	UserId       uint                     `json:"user_id"`
	ProductId    uint                     `json:"product_id"`
	Mobile       string                   `json:"mobile"`
	OrderNo      string                   `json:"order_no"`
	Subject      string                   `json:"subject"`
	Amount       float64                  `json:"amount"`
	Status       types.OrderStatus        `json:"status"`
	PayTime      int64                    `json:"pay_time"`
	PayWay       string                   `json:"pay_way"`
	Detail       []localModel.OrderDetail `json:"remark"`
	OrderType    int                      `json:"order_type"`  //0=商品 1=服务
	VerifyTime   int64                    `json:"verify_time"` //核销时间
	Remark       string
	ExpireDate   int64  `json:"expire_date"`
	UserName     string `json:"user_name"`
	UserPhone    string `json:"user_phone"`
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Addr         string `json:"addr"`
	DeliveryTime string `json:"delivery_time"`
	Invoice      string `json:"invoice"`
	Memo         string `json:"memo"`

	// 酒店订单字段
	HotelCheckinTime  *int64 `json:"hotel_checkin_time,omitempty"`  // 入住时间（时间戳）
	HotelCheckoutTime *int64 `json:"hotel_checkout_time,omitempty"` // 离店时间（时间戳）
	HotelRoomType     string `json:"hotel_room_type,omitempty"`     // 房型

	// 线路订单字段
	TravelPassengers []TravelPassengerVO `json:"travel_passengers,omitempty"` // 出行人信息
}

type OrderCount struct {
	TotalCount  int64 `json:"total_count"`
	WaitPay     int64 `gorm:"column:waitPay" json:"waitPay"`
	WaitReceive int64 `gorm:"column:waitReceive" json:"waitReceive"`
	WaitService int64 `gorm:"column:waitService" json:"waitService"`
	AfterSale   int64 `gorm:"column:afterSale" json:"afterSale"`
}
