package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type ReservationTimeSlot struct {
	vo.GinBaseVo
	SpuId       uint   `json:"spu_id"`
	SlotName    string `json:"slot_name"`
	StartTime   string `json:"start_time"`
	EndTime     string `json:"end_time"`
	MaxCapacity int    `json:"max_capacity"`
	IsActive    bool   `json:"is_active"`
	Sort        int    `json:"sort"`
	WeekDays    string `json:"week_days"`
}
