package handler

import (
	"encoding/json"
	"fmt"
	"haha/store/model"
	"log"
	"sync"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/service/nsq"
	baseUtils "gitee.com/masculine_girl/ginbase/api/utils"
	"gorm.io/gorm"
)

// OrderNsqHandler 订单NSQ消息处理器
type OrderNsqHandler struct {
	pkg.BaseHandler
	db         *gorm.DB
	nsqService *nsq.NSQService
	// 添加实例级的锁，用于保护订单操作
	orderMutex sync.RWMutex
	// 更细粒度的锁：每个订单号一个锁
	orderLocks sync.Map // map[string]*sync.Mutex
}

// NewOrderNsqHandler 创建订单NSQ处理器
func NewOrderNsqHandler(app *core.AppServer, db *gorm.DB, nsqService *nsq.NSQService) *OrderNsqHandler {
	h := OrderNsqHandler{
		db:         db,
		nsqService: nsqService,
		// 初始化锁
		orderMutex: sync.RWMutex{},
		orderLocks: sync.Map{},
	}
	h.App = app
	return &h
}

// HandleMessage 实现 MessageHandler 接口
func (h *OrderNsqHandler) HandleMessage(topic string, message nsq.Message) error {
	switch topic {
	case "haha_spu_order":
		return h.handleOrderTimeoutMessage(message)
	default:
		log.Printf("未知的topic: %s", topic)
		message.Finish()
		return nil
	}
}

// handleOrderTimeoutMessage 处理订单超时消息
func (h *OrderNsqHandler) handleOrderTimeoutMessage(message nsq.Message) error {
	log.Printf("收到订单超时消息: %s", string(message.Body()))

	// 解析订单信息
	var orderInfo OrderInfo
	if err := json.Unmarshal(message.Body(), &orderInfo); err != nil {
		log.Printf("解析订单信息失败: %v", err)
		message.Requeue(100) // 重新入队，100ms后重试
		return err
	}

	// 执行订单自动取消逻辑
	success := h.AutoCancelOrder(orderInfo)
	if success {
		log.Printf("订单 %s 自动取消成功", orderInfo.OrderNo)
		message.Finish() // 标记消息处理完成
		return nil
	}

	log.Printf("订单 %s 自动取消失败", orderInfo.OrderNo)
	message.Requeue(100) // 重新入队
	return nil
}

type OrderInfo struct {
	OrderNo     string              `json:"order_no"`
	Ids         []uint              `json:"ids"`
	OrderDetail []model.OrderDetail `json:"detail"`
}

// getOrderLock 获取订单专用锁（更细粒度的锁控制）
func (h *OrderNsqHandler) getOrderLock(orderNo string) *sync.Mutex {
	lock, _ := h.orderLocks.LoadOrStore(orderNo, &sync.Mutex{})
	return lock.(*sync.Mutex)
}

// releaseOrderLock 释放订单锁（可选，用于清理不再需要的锁）
func (h *OrderNsqHandler) releaseOrderLock(orderNo string) {
	h.orderLocks.Delete(orderNo)
}

func (h *OrderNsqHandler) AutoCancelOrder(orderInfo OrderInfo) bool {
	// 使用订单号级别的细粒度锁（推荐）
	orderLock := h.getOrderLock(orderInfo.OrderNo)
	orderLock.Lock()
	defer orderLock.Unlock()
	defer h.releaseOrderLock(orderInfo.OrderNo) // 处理完成后清理锁

	var order model.Order
	// 1. 先查询订单状态，如果订单不存在或已经不是待支付状态，直接返回成功
	res := h.db.Select("id,user_id,order_no,status,time_slot_id,reservation_date,reservation_status").
		Where("order_no = ? and status = 1", orderInfo.OrderNo).First(&order)
	if res.Error != nil || res.RowsAffected == 0 {
		log.Printf("订单 %s 不存在或状态已变更，无需取消", orderInfo.OrderNo)
		return true
	}

	// 3. 开始数据库事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			log.Printf("订单取消过程中发生panic: %v", r)
		}
	}()

	// 4. 更新订单状态（使用乐观锁防止并发修改）
	result := tx.Model(&order).Where("status = 1").Update("status", 11)
	if result.Error != nil {
		tx.Rollback()
		log.Printf("更新订单状态失败: %v", result.Error)
		return false
	}
	// 如果影响行数为0，说明订单状态已被其他进程修改
	if result.RowsAffected == 0 {
		tx.Rollback()
		log.Printf("订单 %s 状态已被其他进程修改，取消操作", orderInfo.OrderNo)
		return true // 返回true因为订单已经不是待支付状态了
	}

	// 6. 恢复库存（仅对非预约订单）
	if len(orderInfo.OrderDetail) > 0 && len(orderInfo.Ids) > 0 {
		var upStockSql string
		upStockSql = "UPDATE tb_spus SET stock_num = stock_num + CASE id "
		for _, v := range orderInfo.OrderDetail {
			upStockSql += fmt.Sprintf("WHEN %d THEN %d ", v.SpuId, v.SpuNum)
		}
		upStockSql = fmt.Sprintf("%s END WHERE id in (%s)", upStockSql, baseUtils.SliceToString(orderInfo.Ids))

		result = tx.Exec(upStockSql)
		if result.Error != nil {
			tx.Rollback()
			log.Printf("恢复库存失败: %v", result.Error)
			return false
		}
		log.Printf("非预约订单 %s 库存已恢复", orderInfo.OrderNo)
	}

	// 7. 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Printf("提交事务失败: %v", err)
		return false
	}

	orderType := "非预约订单"
	log.Printf("%s %s 自动取消成功", orderType, orderInfo.OrderNo)
	return true
}
