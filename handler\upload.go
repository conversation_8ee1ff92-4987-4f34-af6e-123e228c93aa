package handler

import (
	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/service/oss"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
)

type UploadHandler struct {
	pkg.BaseHandler
	uploaderManager *oss.UploaderManager
}

func NewUploadHandler(app *core.AppServer, manager *oss.UploaderManager) *UploadHandler {
	handler := &UploadHandler{uploaderManager: manager}
	handler.App = app
	return handler
}

func (h *UploadHandler) Upload(c *gin.Context) {
	fileURL, err := h.uploaderManager.GetUploadHandler().PutFile(c, "file")
	if err != nil {
		resp.ERROR(c, err.Error())
		return
	}

	resp.SUCCESS(c, fileURL)
}
