package model

import (
	"time"
)

// UserVip 用户VIP表
type UserVip struct {
	Id            uint64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserId        uint64     `gorm:"column:user_id;not null;index" json:"userId"`
	VipLevelId    uint64     `gorm:"column:vip_level_id;not null;index" json:"vipLevelId"`
	OrderNo       *string    `gorm:"column:order_no" json:"orderNo"`
	StartTime     time.Time  `gorm:"column:start_time;not null" json:"startTime"`
	EndTime       *time.Time `gorm:"column:end_time" json:"endTime"`
	PurchasePrice float64    `gorm:"column:purchase_price;type:decimal(10,2);not null" json:"purchasePrice"`
	PaymentMethod *string    `gorm:"column:payment_method" json:"paymentMethod"`
	Status        string     `gorm:"column:status;not null;default:'ACTIVE'" json:"status"`
	AutoRenew     bool       `gorm:"column:auto_renew;default:0" json:"autoRenew"`
	PurchaseTime  time.Time  `gorm:"column:purchase_time;default:CURRENT_TIMESTAMP" json:"purchaseTime"`
	CreatedAt     time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt     time.Time  `gorm:"column:updated_at;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updatedAt"`

	// 关联字段
	VipLevel *VipLevel `gorm:"foreignKey:VipLevelId" json:"vipLevel,omitempty"`
}

func (UserVip) TableName() string {
	return "tb_user_vip"
}
