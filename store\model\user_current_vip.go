package model

import (
	"time"
)

// UserCurrentVip 用户当前VIP状态
type UserCurrentVip struct {
	UserId        uint64     `gorm:"column:user_id;primaryKey" json:"userId"`
	VipLevelId    *uint64    `gorm:"column:vip_level_id" json:"vipLevelId"`
	VipLevelName  *string    `gorm:"column:vip_level_name" json:"vipLevelName"`
	VipLevelCode  *string    `gorm:"column:vip_level_code" json:"vipLevelCode"`
	VipLevelRank  *int       `gorm:"column:vip_level_rank" json:"vipLevelRank"`
	StartTime     *time.Time `gorm:"column:start_time" json:"startTime"`
	EndTime       *time.Time `gorm:"column:end_time" json:"endTime"`
	DaysRemaining *int       `gorm:"column:days_remaining" json:"daysRemaining"`
	IsExpired     bool       `gorm:"column:is_expired;default:0" json:"isExpired"`
	UpdatedAt     time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

func (UserCurrentVip) TableName() string {
	return "tb_user_current_vip"
}
