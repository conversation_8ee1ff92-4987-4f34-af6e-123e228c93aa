package handler

import (
	"net"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/service/tencent_map"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
)

// TencentMapHandler 腾讯地图IP定位处理器
type TencentMapHandler struct {
	pkg.BaseHandler
	baseClient   *tencent_map.TencentMapClient
	cachedClient *tencent_map.CachedTencentMapClient
}

// NewTencentMapHandler 创建腾讯地图处理器
func NewTencentMapHandler(
	app *core.AppServer,
	baseClient *tencent_map.TencentMapClient,
	cachedClient *tencent_map.CachedTencentMapClient) *TencentMapHandler {
	handler := &TencentMapHandler{baseClient: baseClient, cachedClient: cachedClient}
	handler.App = app
	return handler
}

// GetIPLocation 获取指定IP位置信息
func (h *TencentMapHandler) GetIPLocation(c *gin.Context) {
	// 获取IP参数
	ip := c.Query("ip")

	// 如果未提供IP，则获取客户端IP
	if ip == "" {
		ip = c.ClientIP()
	}

	// 验证IP格式
	if !isValidIP(ip) {
		resp.ERROR(c, "无效的IP地址格式")
		return
	}

	// 调用腾讯地图服务获取位置信息
	locationInfo, err := h.baseClient.GetIPLocation(ip)
	if err != nil {
		// 根据错误类型返回不同的错误信息
		switch err.Error() {
		case "腾讯地图服务未启用":
			resp.ERROR(c, "腾讯地图服务未启用")
		case "腾讯地图API错误":
			resp.ERROR(c, "腾讯地图API返回错误")
		case "请求腾讯地图API失败":
			resp.ERROR(c, "请求腾讯地图API失败")
		default:
			resp.ERROR(c, "获取位置信息失败")
		}
		return
	}

	resp.SUCCESS(c, locationInfo)
}

// GetCurrentIPLocation 获取当前请求IP位置信息
func (h *TencentMapHandler) GetCurrentIPLocation(c *gin.Context) {
	// 获取客户端IP
	ip := c.ClientIP()

	// 调用腾讯地图服务获取位置信息
	locationInfo, err := h.baseClient.GetIPLocation(ip)
	if err != nil {
		// 根据错误类型返回不同的错误信息
		switch err.Error() {
		case "腾讯地图服务未启用":
			resp.ERROR(c, "腾讯地图服务未启用")
		case "腾讯地图API错误":
			resp.ERROR(c, "腾讯地图API返回错误")
		case "请求腾讯地图API失败":
			resp.ERROR(c, "请求腾讯地图API失败")
		default:
			resp.ERROR(c, "获取位置信息失败")
		}
		return
	}

	resp.SUCCESS(c, locationInfo)
}

// GetIPLocationWithCache 获取指定IP位置信息（带缓存版本）
func (h *TencentMapHandler) GetIPLocationWithCache(c *gin.Context) {
	// 获取IP参数
	ip := c.Query("ip")

	// 如果未提供IP，则获取客户端IP
	if ip == "" {
		ip = c.ClientIP()
	}

	// 验证IP格式
	if !isValidIP(ip) {
		resp.ERROR(c, "无效的IP地址格式")
		return
	}

	// 使用带缓存的客户端获取位置信息
	locationInfo, err := h.cachedClient.GetIPLocationWithCache(c.Request.Context(), ip)
	if err != nil {
		// 根据错误类型返回不同的错误信息
		switch err.Error() {
		case "腾讯地图服务未启用":
			resp.ERROR(c, "腾讯地图服务未启用")
		case "腾讯地图API错误":
			resp.ERROR(c, "腾讯地图API返回错误")
		case "请求腾讯地图API失败":
			resp.ERROR(c, "请求腾讯地图API失败")
		case "批量获取缓存失败":
			resp.ERROR(c, "缓存操作失败")
		default:
			resp.ERROR(c, "获取位置信息失败")
		}
		return
	}

	resp.SUCCESS(c, locationInfo)
}

// GetCurrentIPLocationWithCache 获取当前请求IP位置信息（带缓存版本）
func (h *TencentMapHandler) GetCurrentIPLocationWithCache(c *gin.Context) {
	// 获取客户端IP
	ip := c.ClientIP()

	// 使用带缓存的客户端获取位置信息
	locationInfo, err := h.cachedClient.GetIPLocationWithCache(c.Request.Context(), ip)
	if err != nil {
		// 根据错误类型返回不同的错误信息
		switch err.Error() {
		case "腾讯地图服务未启用":
			resp.ERROR(c, "腾讯地图服务未启用")
		case "腾讯地图API错误":
			resp.ERROR(c, "腾讯地图API返回错误")
		case "请求腾讯地图API失败":
			resp.ERROR(c, "请求腾讯地图API失败")
		case "批量获取缓存失败":
			resp.ERROR(c, "缓存操作失败")
		default:
			resp.ERROR(c, "获取位置信息失败")
		}
		return
	}

	resp.SUCCESS(c, locationInfo)
}

// BatchGetLocations 批量IP定位
func (h *TencentMapHandler) BatchGetLocations(c *gin.Context) {
	var request BatchIPRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	// 验证IP列表
	if len(request.IPs) == 0 {
		resp.ERROR(c, "IP列表不能为空")
		return
	}

	if len(request.IPs) > 10 {
		resp.ERROR(c, "单次最多支持查询10个IP地址")
		return
	}

	// 验证IP格式
	for _, ip := range request.IPs {
		if !isValidIP(ip) {
			resp.ERROR(c, "无效的IP地址格式: "+ip)
			return
		}
	}

	// 批量获取位置信息
	results := make(map[string]*tencent_map.LocationInfo)
	for _, ip := range request.IPs {
		locationInfo, err := h.baseClient.GetIPLocation(ip)
		if err != nil {
			// 如果某个IP查询失败，记录错误但继续处理其他IP
			results[ip] = &tencent_map.LocationInfo{
				IP: ip,
			}
		} else {
			results[ip] = locationInfo
		}
	}

	resp.SUCCESS(c, results)
}

// BatchGetLocationsWithCache 批量IP定位（带缓存版本）
func (h *TencentMapHandler) BatchGetLocationsWithCache(c *gin.Context) {
	var request BatchIPRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		resp.ERROR(c, types.InvalidArgs)
		return
	}

	// 验证IP列表
	if len(request.IPs) == 0 {
		resp.ERROR(c, "IP列表不能为空")
		return
	}

	if len(request.IPs) > 50 {
		resp.ERROR(c, "单次最多支持查询50个IP地址")
		return
	}

	// 验证IP格式
	for _, ip := range request.IPs {
		if !isValidIP(ip) {
			resp.ERROR(c, "无效的IP地址格式: "+ip)
			return
		}
	}

	// 使用带缓存的客户端批量获取位置信息
	results, err := h.cachedClient.BatchGetIPLocationsWithCache(c.Request.Context(), request.IPs)
	if err != nil {
		// 根据错误类型返回不同的错误信息
		switch err.Error() {
		case "腾讯地图服务未启用":
			resp.ERROR(c, "腾讯地图服务未启用")
		case "腾讯地图API错误":
			resp.ERROR(c, "腾讯地图API返回错误")
		case "请求腾讯地图API失败":
			resp.ERROR(c, "请求腾讯地图API失败")
		case "批量获取缓存失败":
			resp.ERROR(c, "缓存操作失败")
		default:
			resp.ERROR(c, "批量获取位置信息失败")
		}
		return
	}

	resp.SUCCESS(c, results)
}

// RegisterRoutes 注册路由
func (h *TencentMapHandler) RegisterRoutes(router *gin.RouterGroup) {
	// 基础版本（无缓存）
	router.GET("/ip/location", h.GetIPLocation)            // 获取指定IP位置信息
	router.GET("/ip/current", h.GetCurrentIPLocation)      // 获取当前请求IP位置信息
	router.POST("/ip/location/batch", h.BatchGetLocations) // 批量IP定位

	// 带缓存版本
	router.GET("/ip/location/cached", h.GetIPLocationWithCache)            // 获取指定IP位置信息（带缓存）
	router.GET("/ip/current/cached", h.GetCurrentIPLocationWithCache)      // 获取当前请求IP位置信息（带缓存）
	router.POST("/ip/location/batch/cached", h.BatchGetLocationsWithCache) // 批量IP定位（带缓存）
}

// BatchIPRequest 批量IP请求参数
type BatchIPRequest struct {
	IPs []string `json:"ips" binding:"required"` // IP地址列表
}

// isValidIP 验证IP地址格式
func isValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}
