package handler

import (
	"fmt"
	"haha/service/cat"
	"haha/store/model"
	"haha/store/vo"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseModel "gitee.com/masculine_girl/ginbase/api/store/model"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var catService = cat.CatService{}

type CatHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

func NewCatHandler(app *core.AppServer, db *gorm.DB) *CatHandler {
	h := CatHandler{
		db: db,
	}
	h.App = app
	return &h
}

func (h *CatHandler) List(c *gin.Context) {
	queryParam := h.GetTrim(c, "query")
	var total int64
	var items []model.Cat
	var list = make([]vo.Cat, 0)
	session := h.db.Session(&gorm.Session{})
	// 构建查询条件
	session = session.Model(&model.Cat{})
	if queryParam != "" {
		session, _ = baseModel.BuildQuery(session, queryParam)
	}
	session.Count(&total)
	res := session.Order("sort").Find(&items)
	if res.Error == nil {
		for _, item := range items {
			var catVo vo.Cat
			err := utils.CopyObject(item, &catVo)
			if err == nil {
				catVo.Id = item.Id
				catVo.CreatedAt = item.CreatedAt.Unix()
				catVo.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, catVo)
			} else {
				fmt.Println(err)
			}
		}
	}
	resp.SUCCESS(c, baseVo.NewPage(total, 0, 1, list))
}

func (h *CatHandler) ListTree(c *gin.Context) {
	cats, err := catService.GetCatTree(h.db)
	if err != nil {
		resp.ERROR(c, "获取失败")
		return
	}
	resp.SUCCESS(c, cats)
}
