package utils

import (
	"fmt"
	"os"
	"time"
)

// CheckWxPayConfig 检查微信支付配置
func CheckWxPayConfig() {
	fmt.Println("=== 微信支付配置检查 ===")

	// 检查证书文件
	if _, err := os.Stat("./apiclient_key.pem"); os.IsNotExist(err) {
		fmt.Println("❌ 私钥文件 apiclient_key.pem 不存在")
	} else {
		fmt.Println("✅ 私钥文件存在")
	}

	if _, err := os.Stat("./apiclient_cert.pem"); os.IsNotExist(err) {
		fmt.Println("⚠️  证书文件 apiclient_cert.pem 不存在（可选）")
	} else {
		fmt.Println("✅ 证书文件存在")
	}

	// 服务器时间检查
	fmt.Printf("当前服务器时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("当前时间戳: %d\n", time.Now().Unix())

	fmt.Println("\n=== 常见问题排查 ===")
	fmt.Println("1. 确保 NotifyURL 是微信可访问的公网地址")
	fmt.Println("2. 检查 APIv3Key 是否正确（32位）")
	fmt.Println("3. 检查证书序列号 SerialNo 是否匹配")
	fmt.Println("4. 确保服务器时间准确（与北京时间差异不超过5分钟）")
	fmt.Println("5. 建议使用 HTTPS 而非 HTTP")
}

// ValidateNotifyHeaders 验证回调请求头
func ValidateNotifyHeaders(timestamp, nonce, signature, serial string) bool {
	if timestamp == "" {
		fmt.Println("❌ Wechatpay-Timestamp 头部为空")
		return false
	}

	if nonce == "" {
		fmt.Println("❌ Wechatpay-Nonce 头部为空")
		return false
	}

	if signature == "" {
		fmt.Println("❌ Wechatpay-Signature 头部为空")
		return false
	}

	if serial == "" {
		fmt.Println("❌ Wechatpay-Serial 头部为空")
		return false
	}

	fmt.Println("✅ 所有必需的头部信息都存在")
	return true
}
