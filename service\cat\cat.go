package cat

import (
	"haha/store/vo"

	"gorm.io/gorm"
)

type CatService struct{}

func (catService *CatService) GetCatTree(db *gorm.DB) (cats []vo.CatTree, err error) {
	treeMap, err := getCatTreeMap(db)
	cats = treeMap[0]
	for i := 0; i < len(cats); i++ {
		err = getCatChildrenList(&cats[i], treeMap)
	}
	return cats, err
}

func getCatTreeMap(db *gorm.DB) (treeMap map[uint][]vo.CatTree, err error) {
	var allCats []vo.CatTree
	treeMap = make(map[uint][]vo.CatTree)
	err = db.Table("tb_cats").Order("sort").Find(&allCats).Error
	for _, v := range allCats {
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	return treeMap, err
}

func getCatChildrenList(cats *vo.CatTree, treeMap map[uint][]vo.CatTree) (err error) {
	cats.Children = treeMap[cats.Id]
	for i := 0; i < len(cats.Children); i++ {
		err = getCatChildrenList(&cats.Children[i], treeMap)
	}
	return err
}
