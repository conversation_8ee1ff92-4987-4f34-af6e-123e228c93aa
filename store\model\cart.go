package model

import "gitee.com/masculine_girl/ginbase/api/store/model"

type Cart struct {
	model.GinBaseModel
	UserId   int     `gorm:"not null;index"`              // 用户ID
	SpuId    int     `gorm:"not null"`                    // 商品ID
	Quantity int     `gorm:"not null;default:1"`          // 数量
	Price    float64 `gorm:"type:decimal(10,2);not null"` // 加入购物车时的价格
	Selected bool    `gorm:"default:true"`                // 是否选中
}
