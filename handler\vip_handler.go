package handler

import (
	"haha/service/vip"
	"haha/store/model"
	"haha/store/vo"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type VipHandler struct {
	pkg.BaseHandler
	db         *gorm.DB
	redis      *redis.Client
	vipService *vip.VipService
}

func NewVipHandler(
	app *core.AppServer,
	db *gorm.DB,
	client *redis.Client,
	vipService *vip.VipService) *VipHandler {
	handler := &VipHandler{db: db, redis: client, vipService: vipService}
	handler.App = app
	return handler
}

// ListVipLevels 获取VIP等级列表
func (h *VipHandler) ListVipLevels(c *gin.Context) {
	var vipLevels []model.VipLevel
	res := h.db.Where("is_active = ?", true).Order("sort_order ASC, level_rank ASC").Find(&vipLevels)
	if res.Error != nil {
		resp.ERROR(c, "获取VIP等级列表失败")
		return
	}

	// 转换为VO
	var vipLevelVOs []vo.VipLevelVO
	for _, level := range vipLevels {
		vipLevelVO := vo.VipLevelVO{
			Id:             level.Id,
			LevelName:      level.LevelName,
			LevelCode:      level.LevelCode,
			LevelRank:      level.LevelRank,
			Price:          level.Price,
			DurationMonths: level.DurationMonths,
			Description:    level.Description,
			IconUrl:        level.IconUrl,
			Color:          level.Color,
			IsActive:       level.IsActive,
			SortOrder:      level.SortOrder,
			CreatedAt:      level.CreatedAt,
			UpdatedAt:      level.UpdatedAt,
		}

		// 获取该等级的权益
		var levelBenefits []model.VipLevelBenefit
		h.db.Preload("Benefit").Where("vip_level_id = ? AND is_active = ?", level.Id, true).Find(&levelBenefits)

		var benefitVOs []vo.VipBenefitVO
		for _, lb := range levelBenefits {
			if lb.Benefit != nil {
				benefitVO := vo.VipBenefitVO{
					Id:           lb.Benefit.Id,
					BenefitName:  lb.Benefit.BenefitName,
					BenefitCode:  lb.Benefit.BenefitCode,
					BenefitType:  lb.Benefit.BenefitType,
					Description:  lb.Benefit.Description,
					IconUrl:      lb.Benefit.IconUrl,
					IsActive:     lb.Benefit.IsActive,
					SortOrder:    lb.Benefit.SortOrder,
					CreatedAt:    lb.Benefit.CreatedAt,
					UpdatedAt:    lb.Benefit.UpdatedAt,
					BenefitValue: lb.BenefitValue,
					BenefitLimit: lb.BenefitLimit,
				}
				benefitVOs = append(benefitVOs, benefitVO)
			}
		}
		vipLevelVO.Benefits = benefitVOs
		vipLevelVOs = append(vipLevelVOs, vipLevelVO)
	}

	resp.SUCCESS(c, vipLevelVOs)
}

// GetVipLevel 获取VIP等级详情
func (h *VipHandler) GetVipLevel(c *gin.Context) {
	vipLevelId := c.Query("id")
	if vipLevelId == "" {
		resp.ERROR(c, "VIP等级ID不能为空")
		return
	}

	var vipLevel model.VipLevel
	res := h.db.Where("id = ? AND is_active = ?", vipLevelId, true).First(&vipLevel)
	if res.Error != nil {
		resp.ERROR(c, "VIP等级不存在")
		return
	}

	// 转换为VO
	vipLevelVO := vo.VipLevelVO{
		Id:             vipLevel.Id,
		LevelName:      vipLevel.LevelName,
		LevelCode:      vipLevel.LevelCode,
		LevelRank:      vipLevel.LevelRank,
		Price:          vipLevel.Price,
		DurationMonths: vipLevel.DurationMonths,
		Description:    vipLevel.Description,
		IconUrl:        vipLevel.IconUrl,
		Color:          vipLevel.Color,
		IsActive:       vipLevel.IsActive,
		SortOrder:      vipLevel.SortOrder,
		CreatedAt:      vipLevel.CreatedAt,
		UpdatedAt:      vipLevel.UpdatedAt,
	}

	// 获取该等级的权益
	var levelBenefits []model.VipLevelBenefit
	h.db.Preload("Benefit").Where("vip_level_id = ? AND is_active = ?", vipLevel.Id, true).Find(&levelBenefits)

	var benefitVOs []vo.VipBenefitVO
	for _, lb := range levelBenefits {
		if lb.Benefit != nil {
			benefitVO := vo.VipBenefitVO{
				Id:           lb.Benefit.Id,
				BenefitName:  lb.Benefit.BenefitName,
				BenefitCode:  lb.Benefit.BenefitCode,
				BenefitType:  lb.Benefit.BenefitType,
				Description:  lb.Benefit.Description,
				IconUrl:      lb.Benefit.IconUrl,
				IsActive:     lb.Benefit.IsActive,
				SortOrder:    lb.Benefit.SortOrder,
				CreatedAt:    lb.Benefit.CreatedAt,
				UpdatedAt:    lb.Benefit.UpdatedAt,
				BenefitValue: lb.BenefitValue,
				BenefitLimit: lb.BenefitLimit,
			}
			benefitVOs = append(benefitVOs, benefitVO)
		}
	}
	vipLevelVO.Benefits = benefitVOs

	resp.SUCCESS(c, vipLevelVO)
}

// PurchaseVip VIP购买
func (h *VipHandler) PurchaseVip(c *gin.Context) {
	// 获取当前登录用户
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint64(userIDAny.(float64))

	// 解析请求参数
	var req vo.VipPurchaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	// 调用VIP服务进行购买（创建订单）
	result, err := h.vipService.PurchaseVip(c, userID, req)
	if err != nil {
		resp.ERROR(c, err.Error())
		return
	}

	// 返回订单信息，前端需要调用支付接口
	resp.SUCCESS(c, map[string]interface{}{
		"orderNo":        result.OrderNo,
		"vipLevelId":     result.VipLevelId,
		"vipLevelName":   result.VipLevelName,
		"price":          result.Price,
		"durationMonths": result.DurationMonths,
		"message":        "订单创建成功，请调用支付接口完成支付",
	})
}

// GetUserVipHistory 获取用户VIP购买历史
func (h *VipHandler) GetUserVipHistory(c *gin.Context) {
	// 获取当前登录用户
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint64(userIDAny.(float64))

	var userVips []model.UserVip
	res := h.db.Preload("VipLevel").Where("user_id = ?", userID).Order("created_at DESC").Find(&userVips)
	if res.Error != nil {
		resp.ERROR(c, "获取VIP购买历史失败")
		return
	}

	// 转换为VO
	var userVipVOs []vo.UserVipVO
	for _, userVip := range userVips {
		userVipVO := vo.UserVipVO{
			Id:            userVip.Id,
			UserId:        userVip.UserId,
			VipLevelId:    userVip.VipLevelId,
			OrderNo:       userVip.OrderNo,
			StartTime:     userVip.StartTime,
			EndTime:       userVip.EndTime,
			PurchasePrice: userVip.PurchasePrice,
			PaymentMethod: userVip.PaymentMethod,
			Status:        userVip.Status,
			AutoRenew:     userVip.AutoRenew,
			PurchaseTime:  userVip.PurchaseTime,
			CreatedAt:     userVip.CreatedAt,
			UpdatedAt:     userVip.UpdatedAt,
		}

		// 添加VIP等级信息
		if userVip.VipLevel != nil {
			vipLevelVO := vo.VipLevelVO{
				Id:             userVip.VipLevel.Id,
				LevelName:      userVip.VipLevel.LevelName,
				LevelCode:      userVip.VipLevel.LevelCode,
				LevelRank:      userVip.VipLevel.LevelRank,
				Price:          userVip.VipLevel.Price,
				DurationMonths: userVip.VipLevel.DurationMonths,
				Description:    userVip.VipLevel.Description,
				IconUrl:        userVip.VipLevel.IconUrl,
				Color:          userVip.VipLevel.Color,
				IsActive:       userVip.VipLevel.IsActive,
				SortOrder:      userVip.VipLevel.SortOrder,
				CreatedAt:      userVip.VipLevel.CreatedAt,
				UpdatedAt:      userVip.VipLevel.UpdatedAt,
			}
			userVipVO.VipLevel = &vipLevelVO
		}

		userVipVOs = append(userVipVOs, userVipVO)
	}

	resp.SUCCESS(c, userVipVOs)
}

// GetUserVipBenefits 获取用户当前VIP权益
func (h *VipHandler) GetUserVipBenefits(c *gin.Context) {
	// 获取当前登录用户
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint64(userIDAny.(float64))

	// 获取用户当前有效的VIP权益
	var userVipBenefits []model.UserVipBenefit
	res := h.db.Preload("VipLevel").Preload("Benefit").
		Where("user_id = ? AND status = ? AND is_expired = ?", userID, "ACTIVE", false).
		Find(&userVipBenefits)

	if res.Error != nil {
		resp.ERROR(c, "获取VIP权益失败")
		return
	}

	// 转换为VO
	var benefitVOs []vo.VipBenefitVO
	for _, userBenefit := range userVipBenefits {
		if userBenefit.Benefit != nil {
			benefitVO := vo.VipBenefitVO{
				Id:           userBenefit.Benefit.Id,
				BenefitName:  userBenefit.Benefit.BenefitName,
				BenefitCode:  userBenefit.Benefit.BenefitCode,
				BenefitType:  userBenefit.Benefit.BenefitType,
				Description:  userBenefit.Benefit.Description,
				IconUrl:      userBenefit.Benefit.IconUrl,
				IsActive:     userBenefit.Benefit.IsActive,
				SortOrder:    userBenefit.Benefit.SortOrder,
				CreatedAt:    userBenefit.Benefit.CreatedAt,
				UpdatedAt:    userBenefit.Benefit.UpdatedAt,
				BenefitValue: userBenefit.BenefitValue,
				BenefitLimit: userBenefit.BenefitLimit,
			}
			benefitVOs = append(benefitVOs, benefitVO)
		}
	}

	resp.SUCCESS(c, benefitVOs)
}

// SetAutoRenew 设置自动续费
func (h *VipHandler) SetAutoRenew(c *gin.Context) {
	// 获取当前登录用户
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := uint64(userIDAny.(float64))

	// 解析请求参数
	var req struct {
		AutoRenew bool `json:"autoRenew" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	// 调用VIP服务设置自动续费
	if err := h.vipService.SetAutoRenew(userID, req.AutoRenew); err != nil {
		resp.ERROR(c, err.Error())
		return
	}

	resp.SUCCESS(c, "自动续费设置成功")
}
