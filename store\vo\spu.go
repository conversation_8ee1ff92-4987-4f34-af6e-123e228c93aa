package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type Spu struct {
	vo.GinBaseVo
	Name        string   `json:"name"`
	Photo       string   `json:"photo"`
	Info        string   `json:"info"`
	Price       float64  `json:"price"`
	VipPrice    *float64 `json:"vip_price,omitempty"`
	VipSavings  *float64 `json:"vip_savings,omitempty"`
	Description string   `json:"description"`
	StockNum    int      `json:"stockNum"`
	SalesNum    int      `json:"salesNum"`
	CatId       int      `json:"catId"`
	CatPath     string   `json:"cat_path"`
	Likes       int      `json:"likes"`
	Recommends  *int     `json:"recommends"`

	// 商品类型相关字段
	ProductType string `json:"product_type"` // 商品类型:common=通用 route=线路 hotel=酒店

	// 地理位置相关字段
	Province  string   `json:"province,omitempty"`  // 省份
	City      string   `json:"city,omitempty"`      // 城市
	District  string   `json:"district,omitempty"`  // 区/县
	Address   string   `json:"address,omitempty"`   // 详细地址
	Longitude *float64 `json:"longitude,omitempty"` // 经度
	Latitude  *float64 `json:"latitude,omitempty"`  // 纬度

	// 酒店相关字段
	HotelBrand   string `json:"hotel_brand,omitempty"`   // 酒店品牌
	StarLevel    *int   `json:"star_level,omitempty"`    // 星级 1-5星
	HotelType    string `json:"hotel_type,omitempty"`    // 酒店类型
	Facilities   string `json:"facilities,omitempty"`    // 酒店设施列表
	Services     string `json:"services,omitempty"`      // 酒店服务列表
	CheckinTime  string `json:"checkin_time,omitempty"`  // 入住时间
	CheckoutTime string `json:"checkout_time,omitempty"` // 退房时间
	ContactPhone string `json:"contact_phone,omitempty"` // 酒店联系电话
	RoomType     string `json:"room_type,omitempty"`     // 主要房型
	RoomArea     *int   `json:"room_area,omitempty"`     // 房间面积平米

	// 套餐明细信息
	PackageItems []PackageItem `json:"package_items,omitempty"`

	// 预约相关信息
	SupportReservation bool                  `json:"support_reservation"`  // 是否支持预约
	TimeSlots          []ReservationTimeSlot `json:"time_slots,omitempty"` // 预约时间段（已废弃，请使用专门的时间段接口）
}
