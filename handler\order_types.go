package handler

import (
	"haha/store/model"
	"haha/types"
)

// OrderData 订单创建过程中的数据
type OrderData struct {
	Request      *types.OrderRequest
	ProductIds   []uint
	GoodsMap     map[uint]int
	Products     []model.Spu
	ProductCat   model.Cat
	TotalAmount  float64
	OrderDetails []model.OrderDetail
	OrderNo      string
}

// OrderError 订单错误
type OrderError struct {
	Code    int
	Message string
	Err     error
}
