package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type Cart struct {
	vo.GinBaseVo
	UserId   int     `json:"user_id"`  // 用户ID
	SpuId    int     `json:"spu_id"`   // 商品ID
	Quantity int     `json:"quantity"` // 数量
	Price    float64 `json:"price"`    // 加入购物车时的价格
	Selected bool    `json:"selected"` // 是否选中
	// 关联商品信息
	SpuName  string  `json:"spu_name"`  // 商品名称
	SpuPhoto string  `json:"spu_photo"` // 商品图片
	SpuPrice float64 `json:"spu_price"` // 商品当前价格
	StockNum int     `json:"stock_num"` // 库存数量
}

type CartSummary struct {
	TotalItems    int     `json:"total_items"`    // 购物车商品总数
	SelectedItems int     `json:"selected_items"` // 选中商品数量
	TotalPrice    float64 `json:"total_price"`    // 选中商品总价
}
