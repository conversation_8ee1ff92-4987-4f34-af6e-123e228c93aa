package utils

import (
	"encoding/json"
	"fmt"
	"math"
)

// YuanToFen 将 float64 类型的元转换为 int 类型的分
func YuanToFen(yuan float64) int {
	// 使用 math.Round 进行四舍五入，避免浮点数精度问题
	return int(math.Round(yuan * 100))
}

// 从图片数组字符串中获取第一张图片
func GetFirstImageFromArray(imageArrayStr string) (string, error) {
	var imageUrls []string
	err := json.Unmarshal([]byte(imageArrayStr), &imageUrls)
	if err != nil {
		return "", fmt.Errorf("解析图片数组失败: %v", err)
	}

	if len(imageUrls) == 0 {
		return "", nil
	}

	return imageUrls[0], nil
}
