package handler

import (
	"fmt"
	"haha/store/model"
	"haha/store/vo"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseModel "gitee.com/masculine_girl/ginbase/api/store/model"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BannerHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

func NewBannerHandler(app *core.AppServer, db *gorm.DB) *BannerHandler {
	h := BannerHandler{
		db: db,
	}
	h.App = app
	return &h
}

func (h *BannerHandler) List(c *gin.Context) {
	queryParam := h.GetTrim(c, "query")
	var total int64
	var items []model.Banner
	var list = make([]vo.Banner, 0)
	session := h.db.Session(&gorm.Session{})
	// 构建查询条件
	session = session.Model(&model.Banner{})
	if queryParam != "" {
		session, _ = baseModel.BuildQuery(session, queryParam)
	}
	session.Count(&total)
	res := session.Order("sort").Find(&items)
	if res.Error == nil {
		for _, item := range items {
			var bannerVo vo.Banner
			err := utils.CopyObject(item, &bannerVo)
			if err == nil {
				bannerVo.Id = item.Id
				bannerVo.CreatedAt = item.CreatedAt.Unix()
				bannerVo.UpdatedAt = item.UpdatedAt.Unix()
				list = append(list, bannerVo)
			} else {
				fmt.Println(err)
			}
		}
	}
	resp.SUCCESS(c, baseVo.NewPage(total, 0, 1, list))
}
