package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type Store struct {
	vo.GinBaseVo
	StoreName       string  `json:"storeName"`
	StoreCode       string  `json:"storeCode"`
	Contact<PERSON>erson   string  `json:"contactPerson"`
	ContactPhone    string  `json:"contactPhone"`
	ContactEmail    string  `json:"contactEmail"`
	Province        string  `json:"province"`
	City            string  `json:"city"`
	District        string  `json:"district"`
	Street          string  `json:"street"`
	DetailedAddress string  `json:"detailedAddress"`
	Longitude       float64 `json:"longitude"`
	Latitude        float64 `json:"latitude"`
	BusinessTags    string  `json:"businessTags"`
	BusinessHours   string  `json:"businessHours"`
	StoreImages     string  `json:"storeImages"`
	Status          int     `json:"status"`
	IsFeatured      int     `json:"isFeatured"`
	Sort            int     `json:"sort"`
	Remark          string  `json:"remark"`
}

// StoreListRequest 门店列表请求参数
type StoreListRequest struct {
	Page       int     `json:"page" form:"page"`
	PageSize   int     `json:"page_size" form:"page_size"`
	City       string  `json:"city" form:"city"`               // 城市筛选
	District   string  `json:"district" form:"district"`       // 区县筛选
	Status     *int    `json:"status" form:"status"`           // 状态筛选
	IsFeatured *int    `json:"is_featured" form:"is_featured"` // 是否推荐
	Query      string  `json:"query" form:"query"`             // 搜索关键词
	Sort       string  `json:"sort" form:"sort"`               // 排序方式：sort_desc, distance_asc
	Longitude  float64 `json:"longitude" form:"longitude"`     // 经度（用于距离排序）
	Latitude   float64 `json:"latitude" form:"latitude"`       // 纬度（用于距离排序）
	Radius     float64 `json:"radius" form:"radius"`           // 搜索半径（km）
}

// StoreDetailResponse 门店详情响应
type StoreDetailResponse struct {
	Store
	Distance         float64 `json:"distance,omitempty"` // 距离（km）
	IsOpen           bool    `json:"isOpen"`             // 当前是否营业
	FormattedAddress string  `json:"formattedAddress"`   // 格式化地址
}

// StoreNearbyRequest 附近门店请求参数
type StoreNearbyRequest struct {
	Longitude float64 `json:"longitude" form:"longitude" binding:"required"` // 经度
	Latitude  float64 `json:"latitude" form:"latitude" binding:"required"`   // 纬度
	Radius    float64 `json:"radius" form:"radius"`                          // 搜索半径（km），默认10km
	Limit     int     `json:"limit" form:"limit"`                            // 返回数量，默认20
	Status    *int    `json:"status" form:"status"`                          // 状态筛选
}

// CityStoreCount 城市门店数量统计
type CityStoreCount struct {
	City  string `json:"city"`
	Count int64  `json:"count"`
}
