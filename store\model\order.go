package model

import (
	"haha/types"
	"time"

	"gitee.com/masculine_girl/ginbase/api/store/model"
	"gorm.io/gorm"
)

// Order 订单
type Order struct {
	model.GinBaseModel
	UserId            uint
	ProductId         uint
	Mobile            string
	OrderNo           string
	Subject           string
	Amount            float64
	Status            types.OrderStatus
	Remark            string
	PayTime           int64
	PayWay            string // 支付方式
	DeletedAt         gorm.DeletedAt
	OrderType         int
	VerifyTime        int64 //核销时间
	ExpireDate        time.Time
	UserName          string `json:"user_name"`
	UserPhone         string `json:"user_phone"`
	Province          string `json:"province"`
	City              string `json:"city"`
	District          string `json:"district"`
	Addr              string `json:"addr"`
	DeliveryTime      string `json:"delivery_time"`
	Memo              string `json:"memo"`
	ReservationStatus string `json:"reservation_status"` // 预约状态: pending, confirmed, cancelled

	// 酒店订单字段
	HotelCheckinTime  *time.Time `json:"hotel_checkin_time,omitempty"`  // 入住时间
	HotelCheckoutTime *time.Time `json:"hotel_checkout_time,omitempty"` // 离店时间
	HotelRoomType     string     `json:"hotel_room_type,omitempty"`     // 房型
}
