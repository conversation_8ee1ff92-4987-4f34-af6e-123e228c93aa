package model

import (
	"time"
	"gitee.com/masculine_girl/ginbase/api/store/model"
)

// LotteryActivity 抽奖活动表
type LotteryActivity struct {
	model.GinBaseModel
	ActivityName  string    `gorm:"not null;comment:活动名称" json:"activityName"`
	StartTime     time.Time `gorm:"not null;comment:开始时间" json:"startTime"`
	EndTime       time.Time `gorm:"not null;comment:结束时间" json:"endTime"`
	PointsCost    uint      `gorm:"default:0;comment:每次抽奖消耗积分" json:"pointsCost"`
	Status        uint8     `gorm:"default:0;comment:活动状态 (0:未开始, 1:进行中, 2:已结束)" json:"status"`
	Description   string    `gorm:"type:text;comment:活动描述" json:"description"`
	IsRecommended bool      `gorm:"default:false;comment:是否推荐" json:"isRecommended"`
}

func (LotteryActivity) TableName() string {
	return "tb_lottery_activities"
}
