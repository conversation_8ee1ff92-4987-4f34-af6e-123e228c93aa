package model

import "gitee.com/masculine_girl/ginbase/api/store/model"

type Address struct {
	model.GinBaseModel
	UserId    int    `gorm:"not null;index"`   // 用户ID
	UserName  string `gorm:"size:50;not null"` // 收货人姓名
	UserPhone string `gorm:"size:45;not null"` // 收货人手机号
	Province  string `gorm:"size:45;not null"` // 省份
	City      string `gorm:"size:45;not null"` // 城市
	District  string `gorm:"size:45;not null"` // 区/县
	Addr      string `gorm:"size:200"`         // 详细地址
}

func (Address) TableName() string {
	return "tb_addrs"
}
