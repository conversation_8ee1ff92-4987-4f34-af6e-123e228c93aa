package model

import "gitee.com/masculine_girl/ginbase/api/store/model"

type StoreSpu struct {
	model.GinBaseModel
	StoreId       uint    `gorm:"column:store_id;not null" json:"storeId"`               // 门店ID
	SpuId         uint    `gorm:"column:spu_id;not null" json:"spuId"`                   // 商品ID
	IsActive      int     `gorm:"column:is_active;default:1" json:"isActive"`            // 是否上架：1-上架，0-下架
	StorePrice    float64 `gorm:"column:store_price;not null" json:"storePrice"`         // 门店价格（必填，默认同步商品原价）
	StoreStockNum int     `gorm:"column:store_stock_num;not null" json:"storeStockNum"`  // 门店库存（必填，默认同步商品原库存）
	StoreSalesNum int     `gorm:"column:store_sales_num;default:0" json:"storeSalesNum"` // 门店销量
	SortOrder     int     `gorm:"column:sort_order;default:0" json:"sortOrder"`          // 门店内商品排序
	Remark        string  `gorm:"column:remark" json:"remark"`                           // 备注

	// 关联的门店信息
	Store Store `gorm:"foreignKey:StoreId"`
	// 关联的商品信息
	Spu Spu `gorm:"foreignKey:SpuId"`
}

func (StoreSpu) TableName() string {
	return "tb_store_spus"
}
