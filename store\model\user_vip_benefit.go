package model

import (
	"time"
)

// UserVipBenefit 用户VIP权益记录表
type UserVipBenefit struct {
	Id             uint64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserId         uint64     `gorm:"column:user_id;not null;index" json:"userId"`
	VipLevelId     uint64     `gorm:"column:vip_level_id;not null;index" json:"vipLevelId"`
	BenefitId      uint64     `gorm:"column:benefit_id;not null;index" json:"benefitId"`
	BenefitValue   *string    `gorm:"column:benefit_value" json:"benefitValue"`
	BenefitLimit   *int       `gorm:"column:benefit_limit" json:"benefitLimit"`
	UsedCount      int        `gorm:"column:used_count;default:0" json:"usedCount"`
	RemainingCount *int       `gorm:"column:remaining_count" json:"remainingCount"`
	StartTime      time.Time  `gorm:"column:start_time;not null" json:"startTime"`
	EndTime        *time.Time `gorm:"column:end_time" json:"endTime"`
	Status         string     `gorm:"column:status;not null;default:'ACTIVE'" json:"status"`
	IsExpired      bool       `gorm:"column:is_expired;default:0" json:"isExpired"`
	CreatedAt      time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt      time.Time  `gorm:"column:updated_at;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updatedAt"`

	// 关联字段
	VipLevel *VipLevel   `gorm:"foreignKey:VipLevelId" json:"vipLevel,omitempty"`
	Benefit  *VipBenefit `gorm:"foreignKey:BenefitId" json:"benefit,omitempty"`
}

func (UserVipBenefit) TableName() string {
	return "tb_user_vip_benefits"
}
