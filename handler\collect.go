package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"haha/store/model"
	"log"
	"strconv"
	"strings"
	"time"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type CollectHandler struct {
	pkg.BaseHandler
	db    *gorm.DB
	redis *redis.Client
}

func NewCollectHandler(app *core.AppServer, db *gorm.DB, client *redis.Client) *CollectHandler {
	handler := &CollectHandler{db: db, redis: client}
	handler.App = app
	return handler
}

// 获取Redis键
func getCollectKey(userID, productID string) string {
	return "user:" + userID + ":favorite:" + productID
}

func getProductLikesKey(productID string) string {
	return "product:" + productID + ":likes"
}

// 收藏/取消收藏商品
func (h *CollectHandler) ToggleCollect(c *gin.Context) {
	productID := h.GetTrim(c, "id")
	if productID == "" {
		resp.ERROR(c, "没有传商品ID")
		return
	}
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))
	ctx := context.Background()

	// 1. 使用Lua脚本原子性地更新Redis状态
	isCollect, err := toggleCollectAtomic(ctx, h.redis, strconv.Itoa(userID), productID)
	if err != nil {
		resp.ERROR(c, "失败")
		return
	}
	// 2. 更新商品收藏数
	if isCollect {
		h.redis.Incr(ctx, getProductLikesKey(productID))
	} else {
		h.redis.Decr(ctx, getProductLikesKey(productID))
	}
	// 3. 将操作添加到队列
	enqueueFavoriteOperation(ctx, h.redis, strconv.Itoa(userID), productID, isCollect)
	// 4. 立即返回结果
	resp.SUCCESS(c, gin.H{"is_collect": isCollect})
}

// 使用Redis Lua脚本实现原子操作
func toggleCollectAtomic(ctx context.Context, redisClient *redis.Client, userID, productID string) (bool, error) {
	script := `
        local key = KEYS[1]
        local current = redis.call('GET', key)
        if current == 'true' then
            redis.call('DEL', key)
            return 0
        else
            redis.call('SET', key, 'true', 'EX', 86400)
            return 1
        end
    `

	result, err := redisClient.Eval(ctx, script, []string{getCollectKey(userID, productID)}).Int64()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// 将收藏操作添加到队列
func enqueueFavoriteOperation(ctx context.Context, redisClient *redis.Client, userID, productID string, isCollect bool) {
	operation := map[string]interface{}{
		"user_id":    userID,
		"product_id": productID,
		"is_collect": isCollect,
		"timestamp":  time.Now().UnixNano(),
	}

	jsonData, _ := json.Marshal(operation)
	redisClient.RPush(ctx, "collect_queue", jsonData)
}

// 消费者协程 - 处理队列中的操作
func (h *CollectHandler) StartConsumer(ctx context.Context) error {
	fmt.Println("[StartConsumer] 后台协程已启动")
	for {
		select {
		case <-ctx.Done():
			fmt.Printf("[%s] Context canceled. Shutting down consumer.\n", "StartConsumer")
			return ctx.Err()
		default:
			// 阻塞获取队列中的任务
			result, err := h.redis.BLPop(ctx, 0, "collect_queue").Result()
			if err != nil {
				// 检查错误是否由上下文取消引起
				if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
					fmt.Printf("[%s] Context canceled or deadline exceeded while waiting for task. Shutting down. Error: %v\n", "collect_queue", err)
					return err // 返回原始的上下文错误
				}
				// 如果是 redis.Nil 错误，说明在超时时间内没有元素，是正常情况，继续循环
				if errors.Is(err, redis.Nil) {
					fmt.Printf("[%s] No task in queue, waiting...\n", "collect_queue") // 可以选择性地记录
					continue
				}
				// 其他 Redis 错误
				fmt.Printf("[%s] Error fetching task from queue: %v. Retrying after delay.\n", "collect_queue", err)
				// 在重试前短暂休眠，避免CPU空转
				select {
				case <-time.After(2 * time.Second):
				case <-ctx.Done():
					fmt.Printf("[%s] Context canceled during retry delay. Shutting down.\n", "collect_queue")
					return ctx.Err()
				}
				continue
			}

			// 解析任务数据
			var operation map[string]interface{}
			if err := json.Unmarshal([]byte(result[1]), &operation); err != nil {
				fmt.Printf("解析任务时出错: %v\n", err)
				continue
			}
			// 处理任务（同步到数据库）
			syncOperationToDB(h.db, h.redis, operation)
		}
	}
}

// 同步操作到数据库
func syncOperationToDB(db *gorm.DB, redisClient *redis.Client, operation map[string]interface{}) {
	fmt.Printf("开始同步操作: %+v", operation)
	userID, _ := strconv.Atoi(fmt.Sprintf("%v", operation["user_id"]))
	productID, _ := strconv.Atoi(fmt.Sprintf("%v", operation["product_id"]))
	isCollect := fmt.Sprintf("%v", operation["is_collect"]) == "true"

	db = db.Session(&gorm.Session{SkipHooks: true})

	if isCollect {
		// 添加收藏（使用事务保证唯一性）
		db.Transaction(func(tx *gorm.DB) error {
			var count int64
			tx.Model(&model.Collect{}).Where("user_id = ? AND spu_id = ?", userID, productID).Count(&count)
			if count == 0 {
				return tx.Create(&model.Collect{
					UserId: userID,
					SpuId:  productID,
				}).Error
			}
			return nil
		})
	} else {
		// 取消收藏
		db.Where("user_id = ? AND spu_id = ?", userID, productID).Delete(&model.Collect{})
	}

	// 更新商品收藏数
	var spu model.Spu
	if err := db.Where("id = ?", productID).First(&spu).Error; err == nil {
		// 获取当前 Redis 中的收藏数
		likesStr, _ := redisClient.Get(context.Background(), getProductLikesKey(strconv.Itoa(productID))).Result()
		likes, _ := strconv.Atoi(likesStr)

		// 更新数据库中的收藏数
		db.Model(&spu).Update("likes", likes)
	}

	fmt.Printf("同步操作完成: %+v", operation)
}

// 批量同步商品收藏数
func batchSyncLikesToDB(ctx context.Context, redisClient *redis.Client, db *gorm.DB) {
	// 获取所有商品ID
	productKeys, err := redisClient.Keys(ctx, "product:*:likes").Result()
	if err != nil {
		log.Printf("获取产品key时出错: %v", err)
		return
	}

	// 批量获取收藏数
	pipeline := redisClient.Pipeline()
	cmds := make([]*redis.StringCmd, len(productKeys))

	for i, key := range productKeys {
		cmds[i] = pipeline.Get(ctx, key)
	}

	_, err = pipeline.Exec(ctx)
	if err != nil {
		log.Printf("获取收藏时出错: %v", err)
		return
	}

	// 批量更新数据库
	db.Transaction(func(tx *gorm.DB) error {
		for i, key := range productKeys {
			// 解析商品ID
			parts := strings.Split(key, ":")
			if len(parts) != 3 {
				continue
			}

			productID := parts[1]
			likes, err := cmds[i].Int64()
			if err != nil {
				log.Printf("分析产品的时出错 %s: %v", productID, err)
				continue
			}
			// 更新数据库
			tx.Model(&model.Spu{}).Where("id = ?", productID).Update("likes", likes)
		}
		return nil
	})
}

// 定时任务：检查并修复可能的不一致
func checkAndFixInconsistencies(ctx context.Context, redisClient *redis.Client, db *gorm.DB) {
	// 获取所有用户收藏的商品ID
	userKeys, err := redisClient.Keys(ctx, "user:*:favorite:*").Result()
	if err != nil {
		log.Printf("获取用户key时出错: %v", err)
		return
	}

	for _, key := range userKeys {
		parts := strings.Split(key, ":")
		if len(parts) != 4 {
			continue
		}

		userID := parts[1]
		productID := parts[3]

		// 检查数据库中是否存在该记录
		var count int64
		db.Model(&model.Collect{}).
			Where("user_id = ? AND spu_id = ?", userID, productID).
			Count(&count)

		// 如果Redis中有但数据库中没有，则添加
		if count == 0 {
			userId, _ := strconv.Atoi(userID)
			productId, _ := strconv.Atoi(productID)
			db.Create(&model.Collect{
				UserId: userId,
				SpuId:  productId,
			})
		}
	}
}

func (h *CollectHandler) BatchSyncLikesTask(ctx context.Context) error {
	// 每10分钟同步一次收藏数
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			batchSyncLikesToDB(ctx, h.redis, h.db)
		case <-ctx.Done():
			log.Printf("[%s] Context canceled. Shutting down task gracefully.", "BatchSyncLikesTask")
			return ctx.Err()
		}
	}
}

func (h *CollectHandler) DataConsistencyCheckTask(ctx context.Context) error {
	// 每小时检查一次数据一致性
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			checkAndFixInconsistencies(ctx, h.redis, h.db)
		case <-ctx.Done():
			log.Printf("[%s] Context canceled. Shutting down task gracefully.", "DataConsistencyCheckTask")
			return ctx.Err()
		}
	}
}
