package vo

import (
	"time"
)

// UserVipVO 用户VIP购买记录VO
type UserVipVO struct {
	Id            uint64     `json:"id"`
	UserId        uint64     `json:"userId"`
	VipLevelId    uint64     `json:"vipLevelId"`
	OrderNo       *string    `json:"orderNo"`
	StartTime     time.Time  `json:"startTime"`
	EndTime       *time.Time `json:"endTime"`
	PurchasePrice float64    `json:"purchasePrice"`
	PaymentMethod *string    `json:"paymentMethod"`
	Status        string     `json:"status"`
	AutoRenew     bool       `json:"autoRenew"`
	PurchaseTime  time.Time  `json:"purchaseTime"`
	CreatedAt     time.Time  `json:"createdAt"`
	UpdatedAt     time.Time  `json:"updatedAt"`

	// 扩展字段
	VipLevel *VipLevelVO `json:"vipLevel,omitempty"`
}

// VipPurchaseRequest VIP购买请求
type VipPurchaseRequest struct {
	VipLevelId    uint64  `json:"vipLevelId" binding:"required"`
	PaymentMethod *string `json:"paymentMethod"`
	AutoRenew     bool    `json:"autoRenew"`
}

// VipPurchaseResponse VIP购买响应
type VipPurchaseResponse struct {
	OrderNo        string  `json:"orderNo"`
	VipLevelId     uint64  `json:"vipLevelId"`
	VipLevelName   string  `json:"vipLevelName"`
	Price          float64 `json:"price"`
	DurationMonths int     `json:"durationMonths"`
	PaymentUrl     *string `json:"paymentUrl,omitempty"`
}
