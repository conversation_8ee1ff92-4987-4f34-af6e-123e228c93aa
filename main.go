package main

import (
	"haha/handler"
	"haha/service/pay"
	"haha/service/vip"
	"haha/tasks"

	"gitee.com/masculine_girl/ginbase"
	"gitee.com/masculine_girl/ginbase/api/core"
)

// 声明 BackgroundTaskOptions（已在 background_tasks.go 中定义）
// var BackgroundTaskOptions []fx.Option // 已由 background_tasks.go 提供

func main() {

	// 创建控制器
	controllerProviders := []interface{}{
		pay.NewWxPayService,
		vip.NewVipService,
		handler.NewBannerHandler,
		handler.NewUploadHandler,
		handler.NewMemberHandler,
		handler.NewCatHandler,
		handler.NewSpuHandler,
		handler.NewRouteSpuHandler,
		handler.NewHotelSpuHandler,

		handler.NewCollectHandler,
		handler.NewCartHandler,
		handler.NewAddressHandler,
		handler.NewOrderHandler,
		handler.NewOrderNsqHandler,

		handler.NewStoreH<PERSON><PERSON>,
		handler.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
		handler.NewDictionaries<PERSON>andler,
		handler.NewTencentMapHandler,    // 注册腾讯地图处理器
		handler.NewUserPassengerHandler, // 注册用户出行人处理器
		handler.NewLotteryHandler,       // 注册抽奖处理器
	}

	// 注册路由
	routeRegistrations := []interface{}{
		func(s *core.AppServer, h *handler.MemberHandler) {
			group := s.Engine.Group("/api/user/")
			group.POST("register", h.Register)
			group.POST("login", h.Login)
			group.POST("wx/login", h.WxLogin)
			group.POST("wx/silent-login", h.WxSilentLogin)
			group.GET("profile", h.Profile)
			group.POST("profile/update", h.ProfileUpdate)
			group.POST("password", h.UpdatePass)
			group.POST("resetPass", h.ResetPass)
			group.GET("vip-status", h.VipStatus)           // 获取VIP状态详情
			group.GET("vip-summary", h.VipStatusSummary)   // 获取VIP状态概要
			group.GET("invite/code", h.GenerateInviteCode) // 生成/获取邀请码及二维码
			group.POST("invite/bind", h.BindInviter)       // 补绑上级
			group.GET("invite/stats", h.GetInviteStats)    // 获取邀请统计信息

			// 认证相关路由组
			authGroup := s.Engine.Group("/api/auth/")
			authGroup.POST("refresh", h.RefreshToken) // 刷新token接口
		},
		func(s *core.AppServer, h *handler.BannerHandler) {
			group := s.Engine.Group("/api/banner/")
			group.GET("list", h.List)
		},
		func(s *core.AppServer, h *handler.CatHandler) {
			group := s.Engine.Group("/api/cat/")
			group.GET("list", h.List)
			group.GET("tree", h.ListTree)
		},
		func(s *core.AppServer, h *handler.UploadHandler) {
			group := s.Engine.Group("/api/upload/")
			group.POST("putFile", h.Upload)
		},
		func(s *core.AppServer, h *handler.SpuHandler) {
			group := s.Engine.Group("/api/spu/")
			group.GET("list", h.List)
			group.GET("get", h.Get)
			group.GET("recommend", h.RecommendsList)
		},
		func(s *core.AppServer, h *handler.RouteSpuHandler) {
			group := s.Engine.Group("/api/route-spu/")
			group.GET("list", h.List)
			group.GET("get", h.Get)
			group.GET("recommend", h.RecommendsList)
		},
		func(s *core.AppServer, h *handler.HotelSpuHandler) {
			group := s.Engine.Group("/api/hotel-spu/")
			group.GET("list", h.List)
			group.GET("get", h.Get)
			group.GET("recommend", h.RecommendsList)
			group.GET("nearby", h.GetHotelsByLocation) // 根据地理位置获取酒店
			group.GET("cities", h.GetHotelCities)      // 获取有酒店的城市列表
		},

		func(s *core.AppServer, h *handler.CollectHandler) {
			group := s.Engine.Group("/api/collect/")
			group.GET("exec", h.ToggleCollect)
		},
		func(s *core.AppServer, h *handler.CartHandler) {
			group := s.Engine.Group("/api/cart/")
			group.POST("add", h.AddToCart)           // 添加商品到购物车
			group.GET("list", h.GetCartList)         // 获取购物车列表
			group.PUT("update", h.UpdateCartItem)    // 更新购物车商品
			group.DELETE("remove", h.RemoveFromCart) // 移除购物车商品
			group.GET("summary", h.GetCartSummary)   // 获取购物车汇总
			group.DELETE("clear", h.ClearCart)       // 清空购物车
			group.PUT("select-all", h.SelectAll)     // 全选/取消全选
		},
		func(s *core.AppServer, h *handler.AddressHandler) {
			group := s.Engine.Group("/api/address/")
			group.POST("add", h.AddAddress)               // 添加地址
			group.GET("list", h.GetAddressList)           // 获取地址列表
			group.GET("get", h.GetAddress)                // 获取单个地址详情
			group.PUT("update", h.UpdateAddress)          // 更新地址
			group.DELETE("delete", h.DeleteAddress)       // 删除地址
			group.PUT("set-default", h.SetDefaultAddress) // 设置默认地址
			group.GET("default", h.GetDefaultAddress)     // 获取默认地址
		},
		func(s *core.AppServer, h *handler.OrderHandler) {
			group := s.Engine.Group("/api/order/")
			group.POST("list", h.List)
			group.POST("create/spu", h.CreateSpuOrder)
			group.GET("get", h.GetByOrderNo)
			group.GET("count", h.GetOrderCount) // 获取订单数量

		},

		func(s *core.AppServer, h *handler.StoreHandler) {
			group := s.Engine.Group("/api/store/")
			group.GET("list", h.List)             // 获取门店列表
			group.GET("get", h.Get)               // 获取门店详情
			group.GET("featured", h.GetFeatured)  // 获取推荐门店
			group.GET("nearby", h.GetNearby)      // 获取附近门店
			group.GET("cities", h.GetCities)      // 获取门店城市列表
			group.GET("search", h.Search)         // 搜索门店
			group.GET("by-spu", h.GetStoresBySpu) // 根据商品ID查询门店列表
		},
		func(s *core.AppServer, h *handler.VipHandler) {
			group := s.Engine.Group("/api/vip/")
			group.GET("levels", h.ListVipLevels)        // 获取VIP等级列表
			group.GET("level", h.GetVipLevel)           // 获取VIP等级详情
			group.POST("purchase", h.PurchaseVip)       // VIP购买
			group.GET("history", h.GetUserVipHistory)   // 获取用户VIP购买历史
			group.GET("benefits", h.GetUserVipBenefits) // 获取用户当前VIP权益
			group.POST("auto-renew", h.SetAutoRenew)    // 设置自动续费
		},
		func(s *core.AppServer, h *handler.DictionariesHandler) {
			group := s.Engine.Group("/api/dict/")
			group.GET("list", h.ListByType) // 获取字典列表
		},
		func(s *core.AppServer, h *handler.TencentMapHandler) {
			// 注册腾讯地图相关路由
			apiGroup := s.Engine.Group("/api/tencent-map")
			h.RegisterRoutes(apiGroup)
		},
		func(s *core.AppServer, h *handler.UserPassengerHandler) {
			group := s.Engine.Group("/api/user-passenger/")
			group.POST("create", h.Create)       // 创建用户出行人
			group.GET("list", h.List)            // 获取用户出行人列表
			group.PUT("update/:id", h.Update)    // 更新用户出行人
			group.DELETE("delete/:id", h.Delete) // 删除用户出行人
		},
		func(s *core.AppServer, h *handler.LotteryHandler) {
			group := s.Engine.Group("/api/lottery/")
			group.GET("list", h.List) // 获取抽奖列表
		},
	}
	// 定义跳过登录验证的接口
	skipPath := []string{
		"/api/user/wx/login",
		"/api/user/wx/silent-login",
		"/api/banner/list",
		"/api/cat/list",
		"/api/cat/tree",
		"/api/upload/putFile",
		"/api/spu/list",
		"/api/spu/get",
		"/api/spu/recommend",
		"/api/route-spu/list",
		"/api/route-spu/get",
		"/api/route-spu/recommend",
		"/api/hotel-spu/list",
		"/api/hotel-spu/get",
		"/api/hotel-spu/recommend",
		"/api/hotel-spu/nearby",
		"/api/hotel-spu/cities",
		"/api/store/list",
		"/api/store/get",
		"/api/store/featured",
		"/api/store/nearby",
		"/api/store/cities",
		"/api/store/search",
		"/api/store/by-spu",
		"/api/vip/levels", // VIP等级列表（公开）
		"/api/vip/level",  // VIP等级详情（公开）
		"/api/payment/notify",
		"/api/auth/refresh",                         // 刷新token接口，需要跳过登录验证
		"/api/dict/list",                            // 字典列表接口，需要跳过登录验证
		"/api/tencent-map/ip/location",              // 腾讯地图IP定位（公开）
		"/api/tencent-map/ip/current",               // 腾讯地图当前IP定位（公开）
		"/api/tencent-map/ip/location/batch",        // 腾讯地图批量IP定位（公开）
		"/api/tencent-map/ip/location/cached",       // 腾讯地图IP定位（带缓存，公开）
		"/api/tencent-map/ip/current/cached",        // 腾讯地图当前IP定位（带缓存，公开）
		"/api/tencent-map/ip/location/batch/cached", // 腾讯地图批量IP定位（带缓存，公开）
		"/api/lottery/list",                         // 抽奖列表接口（公开）

	}

	// 运行应用程序
	ginbase.RunApp(controllerProviders, routeRegistrations, skipPath, tasks.BackgroundTaskOptions...)
}
