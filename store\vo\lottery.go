package vo

import (
	"time"
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

// LotteryListItemVO 抽奖列表项VO
type LotteryListItemVO struct {
	vo.GinBaseVo
	// 奖品信息
	PrizeName   string `json:"prizeName"`
	PrizeImage  string `json:"prizeImage"`
	PrizeType   uint8  `json:"prizeType"`
	Description string `json:"description"`

	// 数量信息
	TotalQuantity     uint `json:"totalQuantity"`
	RemainingQuantity uint `json:"remainingQuantity"`

	// 活动信息
	ActivityId    uint      `json:"activityId"`
	ActivityName  string    `json:"activityName"`
	StartTime     time.Time `json:"startTime"`
	EndTime       time.Time `json:"endTime"`
	Status        uint8     `json:"status"`
	PointsCost    uint      `json:"pointsCost"`
	IsRecommended bool      `json:"isRecommended"`
}
