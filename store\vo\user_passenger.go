package vo

import "time"

// UserPassengerVO 用户出行人视图对象
type UserPassengerVO struct {
	Id            uint      `json:"id"`
	UserId        uint      `json:"user_id"`
	Name          string    `json:"name"`
	IdCard        string    `json:"id_card"`
	Phone         string    `json:"phone"`
	PassengerType string    `json:"passenger_type"`
	IsDefault     bool      `json:"is_default"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// UserPassengerRequest 用户出行人请求结构体
type UserPassengerRequest struct {
	Name          string `json:"name" binding:"required"`           // 姓名
	IdCard        string `json:"id_card" binding:"required"`        // 身份证号
	Phone         string `json:"phone" binding:"required"`          // 手机号
	PassengerType string `json:"passenger_type" binding:"required"` // adult(成年人) / child(小孩)
	IsDefault     bool   `json:"is_default"`                        // 是否为默认出行人
}