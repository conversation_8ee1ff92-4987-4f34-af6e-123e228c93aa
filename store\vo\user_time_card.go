package vo

import (
	"time"

	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

// UserTimeCard 用户次卡VO
type UserTimeCard struct {
	vo.GinBaseVo
	UserId         uint      `json:"user_id"`         // 用户ID
	TimeCardId     uint      `json:"time_card_id"`    // 次卡ID
	OrderId        *uint     `json:"order_id"`        // 关联订单ID
	Name           string    `json:"name"`            // 次卡名称
	TotalTimes     int       `json:"total_times"`     // 总次数
	UsedTimes      int       `json:"used_times"`      // 已使用次数
	RemainingTimes int       `json:"remaining_times"` // 剩余次数
	PurchaseTime   time.Time `json:"purchase_time"`   // 购买时间
	ExpireTime     time.Time `json:"expire_time"`     // 过期时间
	PurchasePrice  float64   `json:"purchase_price"`  // 购买价格
	Status         int       `json:"status"`          // 状态: 1-有效, 2-已用完, 3-已过期
	SpuInfo        string    `json:"spu_info"`        // 次卡对应的服务信息（JSON格式）

	// 扩展字段
	StatusText    string `json:"status_text"`    // 状态文本
	IsExpired     bool   `json:"is_expired"`     // 是否已过期
	IsUsedUp      bool   `json:"is_used_up"`     // 是否已用完
	DaysRemaining int    `json:"days_remaining"` // 剩余天数
	UsageProgress string `json:"usage_progress"` // 使用进度百分比
}

// TimeCardPurchaseRequest 次卡购买请求
type TimeCardPurchaseRequest struct {
	TimeCardId   uint    `json:"time_card_id" binding:"required"` // 次卡ID
	Mobile       string  `json:"mobile" binding:"required"`       // 用户手机号
	Amount       float64 `json:"amount" binding:"required"`       // 订单金额
	PayWay       string  `json:"pay_way"`                         // 支付方式
	UserName     string  `json:"user_name"`                       // 用户姓名
	UserPhone    string  `json:"user_phone"`                      // 用户电话
	ServiceNotes string  `json:"service_notes,omitempty"`         // 服务备注（可选）
}

// UserTimeCardListRequest 用户次卡列表请求参数
type UserTimeCardListRequest struct {
	Page     int  `json:"page" form:"page"`
	PageSize int  `json:"page_size" form:"page_size"`
	Status   *int `json:"status" form:"status"` // 状态筛选：1-有效, 2-已用完, 3-已过期
}

// UserTimeCardDetailResponse 用户次卡详情响应
type UserTimeCardDetailResponse struct {
	UserTimeCard
	TimeCardInfo *TimeCard `json:"time_card_info,omitempty"` // 关联的次卡信息
}
