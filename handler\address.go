package handler

import (
	"fmt"
	"haha/store/model"
	"haha/store/vo"
	"strconv"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/core/types"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AddressHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

func NewAddressHandler(app *core.AppServer, db *gorm.DB) *AddressHandler {
	handler := &AddressHandler{db: db}
	handler.App = app
	return handler
}

// AddAddress 添加地址
func (h *AddressHandler) AddAddress(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取请求参数
	var data vo.AddressRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	// 创建地址
	address := model.Address{
		UserId:    userID,
		UserName:  data.UserName,
		UserPhone: data.UserPhone,
		Province:  data.Province,
		City:      data.City,
		District:  data.District,
		Addr:      data.Addr,
	}

	if err := h.db.Create(&address).Error; err != nil {
		resp.ERROR(c, "添加地址失败")
		return
	}

	resp.SUCCESS(c, gin.H{"message": "添加成功", "id": address.Id})
}

// GetAddressList 获取地址列表
func (h *AddressHandler) GetAddressList(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 分页参数
	page := h.GetInt(c, "page", 1)
	pageSize := h.GetInt(c, "page_size", 20)
	offset := (page - 1) * pageSize

	// 获取用户的默认地址ID（容错处理）
	var member model.Member
	var defaultAddrId uint = 0 // 默认值为0，表示没有默认地址
	if err := h.db.Select("default_addr_id").Where("id = ?", userID).First(&member).Error; err == nil {
		defaultAddrId = member.DefaultAddrId
	}
	// 注意：即使查询用户信息失败，我们也继续执行，只是所有地址都不会被标记为默认地址

	// 查询地址列表
	var addresses []model.Address
	query := h.db.Model(model.Address{}).Where("user_id = ?", userID).Order("created_at DESC")

	var total int64
	query.Count(&total)

	if err := query.Offset(offset).Limit(pageSize).Find(&addresses).Error; err != nil {
		fmt.Println(err)
		resp.ERROR(c, "获取地址列表失败")
		return
	}

	// 转换为VO
	var list []vo.Address
	for _, addr := range addresses {
		addressVo := vo.Address{
			UserId:    addr.UserId,
			UserName:  addr.UserName,
			UserPhone: addr.UserPhone,
			Province:  addr.Province,
			City:      addr.City,
			District:  addr.District,
			Addr:      addr.Addr,
			IsDefault: defaultAddrId > 0 && defaultAddrId == addr.Id, // 安全比较：只有当有默认地址ID且匹配时才标记为true
		}
		addressVo.Id = addr.Id
		addressVo.CreatedAt = addr.CreatedAt.Unix()
		addressVo.UpdatedAt = addr.UpdatedAt.Unix()
		list = append(list, addressVo)
	}

	resp.SUCCESS(c, baseVo.NewPage(total, page, pageSize, list))
}

// GetAddress 获取单个地址详情
func (h *AddressHandler) GetAddress(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取地址ID
	addressID := h.GetTrim(c, "id")
	if addressID == "" {
		resp.ERROR(c, "地址ID不能为空")
		return
	}

	// 查询地址
	var address model.Address
	if err := h.db.Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
		resp.ERROR(c, "地址不存在")
		return
	}

	// 获取用户的默认地址ID（容错处理）
	var member model.Member
	var defaultAddrId uint = 0 // 默认值为0，表示没有默认地址
	if err := h.db.Select("default_addr_id").Where("id = ?", userID).First(&member).Error; err == nil {
		defaultAddrId = member.DefaultAddrId
	}

	// 转换为VO
	addressVo := vo.Address{
		UserId:    address.UserId,
		UserName:  address.UserName,
		UserPhone: address.UserPhone,
		Province:  address.Province,
		City:      address.City,
		District:  address.District,
		Addr:      address.Addr,
		IsDefault: defaultAddrId > 0 && defaultAddrId == address.Id, // 安全比较
	}
	addressVo.Id = address.Id
	addressVo.CreatedAt = address.CreatedAt.Unix()
	addressVo.UpdatedAt = address.UpdatedAt.Unix()

	resp.SUCCESS(c, addressVo)
}

// UpdateAddress 更新地址
func (h *AddressHandler) UpdateAddress(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取地址ID
	addressID := h.GetTrim(c, "id")
	if addressID == "" {
		resp.ERROR(c, "地址ID不能为空")
		return
	}

	// 获取请求参数
	var data vo.AddressRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		resp.ERROR(c, "参数错误")
		return
	}

	// 查询地址
	var address model.Address
	if err := h.db.Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
		resp.ERROR(c, "地址不存在")
		return
	}

	// 更新地址
	updateData := map[string]interface{}{
		"user_name":  data.UserName,
		"user_phone": data.UserPhone,
		"province":   data.Province,
		"city":       data.City,
		"district":   data.District,
		"addr":       data.Addr,
	}

	if err := h.db.Model(&address).Updates(updateData).Error; err != nil {
		resp.ERROR(c, "更新地址失败")
		return
	}

	resp.SUCCESS(c, gin.H{"message": "更新成功"})
}

// DeleteAddress 删除地址
func (h *AddressHandler) DeleteAddress(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取地址ID
	addressID := h.GetTrim(c, "id")
	if addressID == "" {
		resp.ERROR(c, "地址ID不能为空")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询地址
	var address model.Address
	if err := tx.Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
		tx.Rollback()
		resp.ERROR(c, "地址不存在")
		return
	}

	// 检查是否为默认地址，如果是则清除用户的默认地址（容错处理）
	var member model.Member
	var isDefaultAddress bool = false
	if err := tx.Select("default_addr_id").Where("id = ?", userID).First(&member).Error; err == nil {
		// 只有查询用户成功时才检查是否为默认地址
		addressIDInt, _ := strconv.Atoi(addressID)
		addressIDUInt := uint(addressIDInt)
		isDefaultAddress = member.DefaultAddrId == addressIDUInt
	}

	if isDefaultAddress {
		// 清除默认地址
		if err := tx.Model(&member).Update("default_addr_id", 0).Error; err != nil {
			tx.Rollback()
			resp.ERROR(c, "清除默认地址失败")
			return
		}
	}

	// 删除地址
	if err := tx.Delete(&address).Error; err != nil {
		tx.Rollback()
		resp.ERROR(c, "删除地址失败")
		return
	}

	tx.Commit()
	resp.SUCCESS(c, gin.H{"message": "删除成功"})
}

// SetDefaultAddress 设置默认地址
func (h *AddressHandler) SetDefaultAddress(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取地址ID
	addressID := h.GetTrim(c, "id")
	if addressID == "" {
		resp.ERROR(c, "地址ID不能为空")
		return
	}

	// 检查地址是否存在
	var address model.Address
	if err := h.db.Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
		resp.ERROR(c, "地址不存在")
		return
	}

	// 更新用户的默认地址ID
	addressIDInt, _ := strconv.Atoi(addressID)
	if err := h.db.Model(&model.Member{}).Where("id = ?", userID).Update("default_addr_id", addressIDInt).Error; err != nil {
		resp.ERROR(c, "设置默认地址失败")
		return
	}

	resp.SUCCESS(c, gin.H{"message": "设置默认地址成功"})
}

// GetDefaultAddress 获取默认地址
func (h *AddressHandler) GetDefaultAddress(c *gin.Context) {
	// 获取用户ID
	userIDAny, exists := c.Get(types.LoginUserID)
	if !exists {
		resp.ERROR(c, "用户未登录")
		return
	}
	userID := int(userIDAny.(float64))

	// 获取用户的默认地址ID（容错处理）
	var member model.Member
	if err := h.db.Select("default_addr_id").Where("id = ?", userID).First(&member).Error; err != nil {
		// 用户不存在或查询失败，返回没有默认地址
		resp.SUCCESS(c, nil)
		return
	}

	if member.DefaultAddrId == 0 {
		resp.SUCCESS(c, nil) // 没有默认地址
		return
	}

	// 查询默认地址
	var address model.Address
	if err := h.db.Where("id = ? AND user_id = ?", member.DefaultAddrId, userID).First(&address).Error; err != nil {
		// 默认地址不存在（可能已被删除），清除用户的默认地址设置并返回null
		h.db.Model(&model.Member{}).Where("id = ?", userID).Update("default_addr_id", 0)
		resp.SUCCESS(c, nil)
		return
	}

	// 转换为VO
	addressVo := vo.Address{
		UserId:    address.UserId,
		UserName:  address.UserName,
		UserPhone: address.UserPhone,
		Province:  address.Province,
		City:      address.City,
		District:  address.District,
		Addr:      address.Addr,
		IsDefault: true,
	}
	addressVo.Id = address.Id
	addressVo.CreatedAt = address.CreatedAt.Unix()
	addressVo.UpdatedAt = address.UpdatedAt.Unix()

	resp.SUCCESS(c, addressVo)
}
