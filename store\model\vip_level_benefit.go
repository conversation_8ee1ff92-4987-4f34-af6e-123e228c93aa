package model

import (
	"time"
)

// VipLevelBenefit VIP等级权益关联表
type VipLevelBenefit struct {
	Id           uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	VipLevelId   uint64    `gorm:"column:vip_level_id;not null;index" json:"vipLevelId"`
	BenefitId    uint64    `gorm:"column:benefit_id;not null;index" json:"benefitId"`
	BenefitValue *string   `gorm:"column:benefit_value" json:"benefitValue"`
	BenefitLimit *int      `gorm:"column:benefit_limit" json:"benefitLimit"`
	IsActive     bool      `gorm:"column:is_active;default:1" json:"isActive"`
	CreatedAt    time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updatedAt"`

	// 关联字段
	VipLevel *VipLevel   `gorm:"foreignKey:VipLevelId" json:"vipLevel,omitempty"`
	Benefit  *VipBenefit `gorm:"foreignKey:BenefitId" json:"benefit,omitempty"`
}

func (VipLevelBenefit) TableName() string {
	return "tb_vip_level_benefits"
}
