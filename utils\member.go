package utils

import (
	"context"
	"errors"
	"haha/store/model"

	"gitee.com/masculine_girl/ginbase/api/core/types"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var ctx = context.Background()

func GetLoginUser(c *gin.Context, db *gorm.DB) (model.Member, error) {
	value, exists := c.Get(types.LoginUserCache)
	if exists {
		return value.(model.Member), nil
	}

	userId, ok := c.Get(types.LoginUserID)
	if !ok {
		return model.Member{}, errors.New("user not login")
	}

	var user model.Member
	res := db.First(&user, userId)
	// 更新缓存
	if res.Error == nil {
		c.Set(types.LoginUserCache, user)
	}
	return user, res.Error
}
