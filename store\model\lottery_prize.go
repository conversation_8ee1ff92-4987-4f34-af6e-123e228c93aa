package model

import (
	"gitee.com/masculine_girl/ginbase/api/store/model"
)

// LotteryPrize 奖品表
type LotteryPrize struct {
	model.GinBaseModel
	PrizeName   string `gorm:"not null;comment:奖品名称" json:"prizeName"`
	PrizeType   uint8  `gorm:"default:1;comment:奖品类型 (1:虚拟物品, 2:实物)" json:"prizeType"`
	PrizeImage  string `gorm:"comment:奖品图片URL" json:"prizeImage"`
	Description string `gorm:"type:text;comment:奖品描述" json:"description"`
}

func (LotteryPrize) TableName() string {
	return "tb_lottery_prizes"
}
