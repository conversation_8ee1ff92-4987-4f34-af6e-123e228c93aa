package vo

// UserCurrentVip 用户当前VIP状态VO
type UserCurrentVip struct {
	UserId        uint64  `json:"user_id"`
	VipLevelId    *uint64 `json:"vip_level_id"`
	VipLevelName  *string `json:"vip_level_name"`
	VipLevelCode  *string `json:"vip_level_code"`
	VipLevelRank  *int    `json:"vip_level_rank"`
	StartTime     *int64  `json:"start_time"` // 转换为时间戳
	EndTime       *int64  `json:"end_time"`   // 转换为时间戳
	DaysRemaining *int    `json:"days_remaining"`
	IsExpired     bool    `json:"is_expired"`
	UpdatedAt     int64   `json:"updated_at"` // 转换为时间戳

	// 扩展字段
	IsVip         bool   `json:"is_vip"`          // 是否为VIP用户
	VipStatus     string `json:"vip_status"`      // VIP状态：active(有效), expired(已过期), none(非VIP)
	VipStatusText string `json:"vip_status_text"` // VIP状态文本
}

// VipStatusSummary VIP状态概要信息（简化版本）
type VipStatusSummary struct {
	IsVip         bool   `json:"is_vip"`
	VipLevelName  string `json:"vip_level_name"`
	VipStatus     string `json:"vip_status"`
	DaysRemaining int    `json:"days_remaining"`
}
