package model

import "gitee.com/masculine_girl/ginbase/api/store/model"

// UserPassenger 用户出行人信息
type UserPassenger struct {
	model.GinBaseModel
	UserId        uint   `json:"user_id"`        // 关联用户ID
	Name          string `json:"name"`           // 姓名
	IdCard        string `json:"id_card"`        // 身份证号
	Phone         string `json:"phone"`          // 手机号
	PassengerType string `json:"passenger_type"` // adult(成年人) / child(小孩)
	IsDefault     bool   `json:"is_default"`     // 是否为默认出行人
}