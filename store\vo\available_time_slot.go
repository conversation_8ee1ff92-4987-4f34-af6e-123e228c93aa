package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

// AvailableTimeSlot 可用时间段（包含库存信息）
type AvailableTimeSlot struct {
	vo.GinBaseVo
	SpuId          uint   `json:"spu_id"`
	SlotName       string `json:"slot_name"`
	StartTime      string `json:"start_time"`
	EndTime        string `json:"end_time"`
	DisplayName    string `json:"display_name"`    // 显示名称（优先显示slot_name，否则显示时间段）
	MaxCapacity    int    `json:"max_capacity"`    // 最大容量
	BookedCount    int    `json:"booked_count"`    // 已预约数量
	AvailableCount int    `json:"available_count"` // 可用数量
	IsAvailable    bool   `json:"is_available"`    // 是否可预约
	Sort           int    `json:"sort"`
	WeekDays       string `json:"week_days"`
}
