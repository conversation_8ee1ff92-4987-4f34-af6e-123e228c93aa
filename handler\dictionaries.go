package handler

import (
	"fmt"
	"haha/store/model"
	"haha/store/vo"

	"gitee.com/masculine_girl/ginbase/api/core"
	"gitee.com/masculine_girl/ginbase/api/pkg"
	baseVo "gitee.com/masculine_girl/ginbase/api/store/vo"
	"gitee.com/masculine_girl/ginbase/api/utils"
	"gitee.com/masculine_girl/ginbase/api/utils/resp"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DictionariesHandler struct {
	pkg.BaseHandler
	db *gorm.DB
}

func NewDictionariesHandler(app *core.AppServer, db *gorm.DB) *DictionariesHandler {
	h := DictionariesHandler{
		db: db,
	}
	h.App = app
	return &h
}

func (h *DictionariesHandler) ListByType(c *gin.Context) {
	typeID := h.GetInt(c, "type", 0)
	if typeID == 0 {
		resp.ERROR(c, "缺少参数")
		return
	}
	var total int64
	var items []model.SysDictionaryDetail
	var list = make([]vo.SysDictionaryDetail, 0)
	session := h.db.Session(&gorm.Session{})
	// 构建查询条件
	session = session.Model(&model.SysDictionaryDetail{})

	if typeID > 0 {
		session = session.Where("sys_dictionary_id = ?", typeID)
	}

	session.Count(&total)
	res := session.Order("sort").Find(&items)
	if res.Error == nil {
		for _, item := range items {
			var SysDictionaryDetailVo vo.SysDictionaryDetail
			err := utils.CopyObject(item, &SysDictionaryDetailVo)
			if err == nil {
				SysDictionaryDetailVo.Id = item.Id
				list = append(list, SysDictionaryDetailVo)
			} else {
				fmt.Println(err)
			}
		}
	}
	resp.SUCCESS(c, baseVo.NewPage(total, 0, 1, list))
}
