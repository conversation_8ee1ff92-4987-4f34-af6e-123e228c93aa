package vo

import (
	"gitee.com/masculine_girl/ginbase/api/store/vo"
)

type Cat struct {
	vo.GinBaseVo
	Name      string `json:"name"`
	Photo     string `json:"photo"`
	Sort      int    `json:"sort"`
	ClassType int    `json:"class_type"`
}

type CatTree struct {
	Id        uint      `json:"id"`
	Name      string    `json:"name"`
	Photo     string    `json:"photo"`
	ParentId  uint      `json:"parent_id"`
	Sort      int       `json:"sort"`
	ClassType int       `json:"class_type"`
	Children  []CatTree `json:"children" gorm:"-"`
}
