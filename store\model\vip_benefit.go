package model

import (
	"time"
)

// VipBenefit VIP权益表
type VipBenefit struct {
	Id          uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	BenefitName string    `gorm:"column:benefit_name;not null" json:"benefitName"`
	BenefitCode *string   `gorm:"column:benefit_code" json:"benefitCode"`
	BenefitType string    `gorm:"column:benefit_type;not null" json:"benefitType"`
	Description *string   `gorm:"column:description;type:text" json:"description"`
	IconUrl     *string   `gorm:"column:icon_url" json:"iconUrl"`
	IsActive    bool      `gorm:"column:is_active;default:1" json:"isActive"`
	SortOrder   int       `gorm:"column:sort_order;default:0" json:"sortOrder"`
	CreatedAt   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updatedAt"`
}

func (VipBenefit) TableName() string {
	return "tb_vip_benefits"
}
