package model

import "gitee.com/masculine_girl/ginbase/api/store/model"

type Store struct {
	model.GinBaseModel
	StoreName       string  `gorm:"column:store_name" json:"storeName"`
	StoreCode       string  `gorm:"column:store_code" json:"storeCode"`
	<PERSON><PERSON><PERSON>   string  `gorm:"column:contact_person" json:"contact<PERSON>erson"`
	ContactPhone    string  `gorm:"column:contact_phone" json:"contactPhone"`
	ContactEmail    string  `gorm:"column:contact_email" json:"contactEmail"`
	Province        string  `gorm:"column:province" json:"province"`
	City            string  `gorm:"column:city" json:"city"`
	District        string  `gorm:"column:district" json:"district"`
	Street          string  `gorm:"column:street" json:"street"`
	DetailedAddress string  `gorm:"column:detailed_address" json:"detailedAddress"`
	Longitude       float64 `gorm:"column:longitude" json:"longitude"`
	Latitude        float64 `gorm:"column:latitude" json:"latitude"`
	BusinessTags    string  `gorm:"column:business_tags" json:"businessTags"`       // JSON数组字符串
	BusinessHours   string  `gorm:"column:business_hours" json:"businessHours"`     // JSON对象字符串
	StoreImages     string  `gorm:"column:store_images" json:"storeImages"`         // JSON数组字符串
	Status          int     `gorm:"column:status;default:1" json:"status"`          // 1正常营业 2暂停营业 3装修中 4已关闭
	IsFeatured      int     `gorm:"column:is_featured;default:0" json:"isFeatured"` // 1是 0否
	Sort            int     `gorm:"column:sort;default:0" json:"sort"`              // 排序值
	Remark          string  `gorm:"column:remark" json:"remark"`
}

func (Store) TableName() string {
	return "tb_stores"
}
