package model

import (
	"gitee.com/masculine_girl/ginbase/api/store/model"
)

type Member struct {
	model.GinBaseModel
	Nick             string
	Mobile           string
	Password         string
	Avatar           string
	Salt             string // 密码盐
	AgentCalls       int    // 剩余智能体创建次数
	Status           bool   `gorm:"default:true"` // 当前状态
	LastLoginAt      int64  // 最后登录时间
	LastLoginIp      string // 最后登录 IP
	OpenId           string
	Yue              int
	CardCode         string
	SqAuthStatus     int
	Types            int
	DefaultAddrId    uint   // 默认地址ID
	ParentId         uint   `gorm:"column:parent_id"`         // 上级ID(邀请人)
	InviteCode       string `gorm:"column:invite_code"`       // 邀请码
	InviteQrcodeUrl  string `gorm:"column:invite_qrcode_url"` // 邀请二维码地址
}
